/// 小说生成相关的数据模型
library novel_models;

/// 小说数据模型
class NovelData {
  final String title;
  final List<String> genres;
  final String theme;
  final String targetReaders;
  final String background;
  final String characters;
  final String writingStylePrompt;
  final String knowledgeBase;
  final String otherRequirements;

  const NovelData({
    required this.title,
    required this.genres,
    required this.theme,
    required this.targetReaders,
    required this.background,
    required this.characters,
    required this.writingStylePrompt,
    required this.knowledgeBase,
    required this.otherRequirements,
  });

  factory NovelData.fromJson(Map<String, dynamic> json) {
    return NovelData(
      title: json['title'] ?? '',
      genres: List<String>.from(json['genres'] ?? []),
      theme: json['theme'] ?? '',
      targetReaders: json['targetReaders'] ?? '',
      background: json['background'] ?? '',
      characters: json['characters'] ?? '',
      writingStylePrompt: json['writingStylePrompt'] ?? '',
      knowledgeBase: json['knowledgeBase'] ?? '',
      otherRequirements: json['otherRequirements'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'genres': genres,
      'theme': theme,
      'targetReaders': targetReaders,
      'background': background,
      'characters': characters,
      'writingStylePrompt': writingStylePrompt,
      'knowledgeBase': knowledgeBase,
      'otherRequirements': otherRequirements,
    };
  }

  NovelData copyWith({
    String? title,
    List<String>? genres,
    String? theme,
    String? targetReaders,
    String? background,
    String? characters,
    String? writingStylePrompt,
    String? knowledgeBase,
    String? otherRequirements,
  }) {
    return NovelData(
      title: title ?? this.title,
      genres: genres ?? this.genres,
      theme: theme ?? this.theme,
      targetReaders: targetReaders ?? this.targetReaders,
      background: background ?? this.background,
      characters: characters ?? this.characters,
      writingStylePrompt: writingStylePrompt ?? this.writingStylePrompt,
      knowledgeBase: knowledgeBase ?? this.knowledgeBase,
      otherRequirements: otherRequirements ?? this.otherRequirements,
    );
  }
}

/// 章节数据模型
class ChapterData {
  final int chapterNumber;
  final String title;
  final String content;
  final int wordCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const ChapterData({
    required this.chapterNumber,
    required this.title,
    required this.content,
    required this.wordCount,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  factory ChapterData.fromJson(Map<String, dynamic> json) {
    return ChapterData(
      chapterNumber: json['chapterNumber'] ?? 0,
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      wordCount: json['wordCount'] ?? 0,
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'chapterNumber': chapterNumber,
      'title': title,
      'content': content,
      'wordCount': wordCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  ChapterData copyWith({
    int? chapterNumber,
    String? title,
    String? content,
    int? wordCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ChapterData(
      chapterNumber: chapterNumber ?? this.chapterNumber,
      title: title ?? this.title,
      content: content ?? this.content,
      wordCount: wordCount ?? this.wordCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// 角色数据模型
class CharacterData {
  final String name;
  final String description;
  final String personality;
  final String background;
  final String appearance;
  final Map<String, dynamic>? attributes;

  const CharacterData({
    required this.name,
    required this.description,
    required this.personality,
    required this.background,
    required this.appearance,
    this.attributes,
  });

  factory CharacterData.fromJson(Map<String, dynamic> json) {
    return CharacterData(
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      personality: json['personality'] ?? '',
      background: json['background'] ?? '',
      appearance: json['appearance'] ?? '',
      attributes: json['attributes'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'personality': personality,
      'background': background,
      'appearance': appearance,
      'attributes': attributes,
    };
  }
}

/// 世界观数据模型
class WorldBuildingData {
  final String name;
  final String description;
  final String geography;
  final String culture;
  final String history;
  final String rules;
  final Map<String, dynamic>? settings;

  const WorldBuildingData({
    required this.name,
    required this.description,
    required this.geography,
    required this.culture,
    required this.history,
    required this.rules,
    this.settings,
  });

  factory WorldBuildingData.fromJson(Map<String, dynamic> json) {
    return WorldBuildingData(
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      geography: json['geography'] ?? '',
      culture: json['culture'] ?? '',
      history: json['history'] ?? '',
      rules: json['rules'] ?? '',
      settings: json['settings'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'geography': geography,
      'culture': culture,
      'history': history,
      'rules': rules,
      'settings': settings,
    };
  }
}

/// 情节线数据模型
class PlotLineData {
  final String name;
  final String description;
  final String status; // introduced, developing, resolved
  final List<int> involvedChapters;
  final Map<String, dynamic>? details;

  const PlotLineData({
    required this.name,
    required this.description,
    required this.status,
    required this.involvedChapters,
    this.details,
  });

  factory PlotLineData.fromJson(Map<String, dynamic> json) {
    return PlotLineData(
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      status: json['status'] ?? 'introduced',
      involvedChapters: List<int>.from(json['involvedChapters'] ?? []),
      details: json['details'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'status': status,
      'involvedChapters': involvedChapters,
      'details': details,
    };
  }
}
