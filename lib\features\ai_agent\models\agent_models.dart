/// Agent系统的数据模型定义
library agent_models;

/// Agent模式枚举
enum AgentMode {
  chat,    // 聊天模式 - 仅提供建议
  agent,   // Agent模式 - 自主执行任务
}

/// 任务类型枚举
enum TaskType {
  createChapters,      // 创建章节
  editChapters,        // 编辑章节
  analyzePlot,         // 情节分析
  characterDevelopment, // 角色发展
  worldBuilding,       // 世界观构建
}

/// 任务复杂度枚举
enum TaskComplexity {
  simple,    // 简单
  moderate,  // 中等
  complex,   // 复杂
}

/// 风险等级枚举
enum RiskLevel {
  low,     // 低风险
  medium,  // 中等风险
  high,    // 高风险
}

/// 步骤类型枚举
enum StepType {
  analyze,   // 分析
  create,    // 创建
  edit,      // 编辑
  validate,  // 验证
}

/// 工具类型枚举
enum ToolType {
  chapterCreator,      // 章节创建工具
  chapterEditor,       // 章节编辑工具
  contentRetriever,    // 内容检索工具
  coherenceValidator,  // 连贯性验证工具
}

/// 执行状态枚举
enum ExecutionStatus {
  pending,     // 等待中
  running,     // 执行中
  completed,   // 已完成
  failed,      // 失败
  cancelled,   // 已取消
  paused,      // 已暂停
}

/// 任务分析结果
class TaskAnalysis {
  final TaskType taskType;
  final TaskComplexity complexity;
  final List<int> targetChapters;
  final List<String> dependencies;
  final int estimatedSteps;
  final RiskLevel riskLevel;
  final String description;
  final List<String> requirements;
  final List<String> contextNeeded;

  const TaskAnalysis({
    required this.taskType,
    required this.complexity,
    required this.targetChapters,
    required this.dependencies,
    required this.estimatedSteps,
    required this.riskLevel,
    required this.description,
    required this.requirements,
    required this.contextNeeded,
  });

  factory TaskAnalysis.fromJson(Map<String, dynamic> json) {
    return TaskAnalysis(
      taskType: TaskType.values.firstWhere(
        (e) => e.name == json['task_type']?.replaceAll('_', ''),
        orElse: () => TaskType.createChapters,
      ),
      complexity: TaskComplexity.values.firstWhere(
        (e) => e.name == json['complexity'],
        orElse: () => TaskComplexity.moderate,
      ),
      targetChapters: List<int>.from(json['target_chapters'] ?? []),
      dependencies: List<String>.from(json['dependencies'] ?? []),
      estimatedSteps: json['estimated_steps'] ?? 1,
      riskLevel: RiskLevel.values.firstWhere(
        (e) => e.name == json['risk_level'],
        orElse: () => RiskLevel.medium,
      ),
      description: json['description'] ?? '',
      requirements: List<String>.from(json['requirements'] ?? []),
      contextNeeded: List<String>.from(json['context_needed'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'task_type': taskType.name,
      'complexity': complexity.name,
      'target_chapters': targetChapters,
      'dependencies': dependencies,
      'estimated_steps': estimatedSteps,
      'risk_level': riskLevel.name,
      'description': description,
      'requirements': requirements,
      'context_needed': contextNeeded,
    };
  }
}

/// 执行步骤
class ExecutionStep {
  final int stepId;
  final StepType stepType;
  final String description;
  final ToolType toolRequired;
  final Map<String, dynamic> inputData;
  final String expectedOutput;
  final List<String> validationCriteria;
  final ExecutionStatus status;
  final String? result;
  final String? error;

  const ExecutionStep({
    required this.stepId,
    required this.stepType,
    required this.description,
    required this.toolRequired,
    required this.inputData,
    required this.expectedOutput,
    required this.validationCriteria,
    this.status = ExecutionStatus.pending,
    this.result,
    this.error,
  });

  ExecutionStep copyWith({
    ExecutionStatus? status,
    String? result,
    String? error,
  }) {
    return ExecutionStep(
      stepId: stepId,
      stepType: stepType,
      description: description,
      toolRequired: toolRequired,
      inputData: inputData,
      expectedOutput: expectedOutput,
      validationCriteria: validationCriteria,
      status: status ?? this.status,
      result: result ?? this.result,
      error: error ?? this.error,
    );
  }

  factory ExecutionStep.fromJson(Map<String, dynamic> json) {
    return ExecutionStep(
      stepId: json['step_id'] ?? 0,
      stepType: StepType.values.firstWhere(
        (e) => e.name == json['step_type'],
        orElse: () => StepType.create,
      ),
      description: json['description'] ?? '',
      toolRequired: ToolType.values.firstWhere(
        (e) => e.name == json['tool_required']?.replaceAll('_', ''),
        orElse: () => ToolType.chapterCreator,
      ),
      inputData: Map<String, dynamic>.from(json['input_data'] ?? {}),
      expectedOutput: json['expected_output'] ?? '',
      validationCriteria: List<String>.from(json['validation_criteria'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'step_id': stepId,
      'step_type': stepType.name,
      'description': description,
      'tool_required': toolRequired.name,
      'input_data': inputData,
      'expected_output': expectedOutput,
      'validation_criteria': validationCriteria,
      'status': status.name,
      'result': result,
      'error': error,
    };
  }
}

/// 回滚点
class RollbackPoint {
  final int stepId;
  final String description;
  final Map<String, dynamic> state;

  const RollbackPoint({
    required this.stepId,
    required this.description,
    required this.state,
  });

  factory RollbackPoint.fromJson(Map<String, dynamic> json) {
    return RollbackPoint(
      stepId: json['step_id'] ?? 0,
      description: json['description'] ?? '',
      state: Map<String, dynamic>.from(json['state'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'step_id': stepId,
      'description': description,
      'state': state,
    };
  }
}

/// 用户确认需求
class UserConfirmation {
  final int stepId;
  final String reason;
  final bool isRequired;
  final bool? userResponse;

  const UserConfirmation({
    required this.stepId,
    required this.reason,
    this.isRequired = true,
    this.userResponse,
  });

  UserConfirmation copyWith({
    bool? userResponse,
  }) {
    return UserConfirmation(
      stepId: stepId,
      reason: reason,
      isRequired: isRequired,
      userResponse: userResponse ?? this.userResponse,
    );
  }

  factory UserConfirmation.fromJson(Map<String, dynamic> json) {
    return UserConfirmation(
      stepId: json['step_id'] ?? 0,
      reason: json['reason'] ?? '',
      isRequired: json['is_required'] ?? true,
      userResponse: json['user_response'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'step_id': stepId,
      'reason': reason,
      'is_required': isRequired,
      'user_response': userResponse,
    };
  }
}

/// 执行计划
class ExecutionPlan {
  final String planId;
  final int totalSteps;
  final List<int> checkpoints;
  final List<ExecutionStep> executionSteps;
  final List<RollbackPoint> rollbackPoints;
  final String estimatedDuration;
  final List<UserConfirmation> userConfirmationsNeeded;
  final ExecutionStatus status;
  final int currentStepIndex;

  const ExecutionPlan({
    required this.planId,
    required this.totalSteps,
    required this.checkpoints,
    required this.executionSteps,
    required this.rollbackPoints,
    required this.estimatedDuration,
    required this.userConfirmationsNeeded,
    this.status = ExecutionStatus.pending,
    this.currentStepIndex = 0,
  });

  ExecutionPlan copyWith({
    ExecutionStatus? status,
    int? currentStepIndex,
    List<ExecutionStep>? executionSteps,
    List<UserConfirmation>? userConfirmationsNeeded,
  }) {
    return ExecutionPlan(
      planId: planId,
      totalSteps: totalSteps,
      checkpoints: checkpoints,
      executionSteps: executionSteps ?? this.executionSteps,
      rollbackPoints: rollbackPoints,
      estimatedDuration: estimatedDuration,
      userConfirmationsNeeded: userConfirmationsNeeded ?? this.userConfirmationsNeeded,
      status: status ?? this.status,
      currentStepIndex: currentStepIndex ?? this.currentStepIndex,
    );
  }

  factory ExecutionPlan.fromJson(Map<String, dynamic> json) {
    return ExecutionPlan(
      planId: json['plan_id'] ?? '',
      totalSteps: json['total_steps'] ?? 0,
      checkpoints: List<int>.from(json['checkpoints'] ?? []),
      executionSteps: (json['execution_steps'] as List?)
          ?.map((e) => ExecutionStep.fromJson(e))
          .toList() ?? [],
      rollbackPoints: (json['rollback_points'] as List?)
          ?.map((e) => RollbackPoint.fromJson(e))
          .toList() ?? [],
      estimatedDuration: json['estimated_duration'] ?? '',
      userConfirmationsNeeded: (json['user_confirmations_needed'] as List?)
          ?.map((e) => UserConfirmation.fromJson(e))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'plan_id': planId,
      'total_steps': totalSteps,
      'checkpoints': checkpoints,
      'execution_steps': executionSteps.map((e) => e.toJson()).toList(),
      'rollback_points': rollbackPoints.map((e) => e.toJson()).toList(),
      'estimated_duration': estimatedDuration,
      'user_confirmations_needed': userConfirmationsNeeded.map((e) => e.toJson()).toList(),
      'status': status.name,
      'current_step_index': currentStepIndex,
    };
  }
}
