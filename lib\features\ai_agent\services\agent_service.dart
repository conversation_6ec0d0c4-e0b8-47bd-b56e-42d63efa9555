/// Agent核心服务
library agent_service;

import 'dart:convert';
import 'dart:async';
import 'package:langchain/langchain.dart';
import '../models/agent_models.dart';
import '../tools/agent_tools.dart';
import '../../novel_generation/models/novel_models.dart';
import '../../../langchain/prompts/novel_prompt_templates_enhanced.dart';

/// Agent执行事件
abstract class AgentEvent {}

class AgentStarted extends AgentEvent {
  final String taskDescription;
  AgentStarted(this.taskDescription);
}

class AgentStepStarted extends AgentEvent {
  final ExecutionStep step;
  AgentStepStarted(this.step);
}

class AgentStepCompleted extends AgentEvent {
  final ExecutionStep step;
  final ToolResult result;
  AgentStepCompleted(this.step, this.result);
}

class AgentStepFailed extends AgentEvent {
  final ExecutionStep step;
  final String error;
  AgentStepFailed(this.step, this.error);
}

class AgentCompleted extends AgentEvent {
  final ExecutionPlan plan;
  AgentCompleted(this.plan);
}

class AgentFailed extends AgentEvent {
  final String error;
  AgentFailed(this.error);
}

class AgentPaused extends AgentEvent {
  final String reason;
  AgentPaused(this.reason);
}

class UserConfirmationRequired extends AgentEvent {
  final UserConfirmation confirmation;
  UserConfirmationRequired(this.confirmation);
}

/// Agent核心服务
class AgentService {
  final dynamic _llm; // 暂时使用dynamic避免类型错误
  final AgentToolManager _toolManager;
  final StreamController<AgentEvent> _eventController;
  
  ExecutionPlan? _currentPlan;
  bool _isExecuting = false;
  bool _isPaused = false;

  AgentService({
    required dynamic llm,
    required AgentToolManager toolManager,
  }) : _llm = llm,
       _toolManager = toolManager,
       _eventController = StreamController<AgentEvent>.broadcast();

  /// 获取工具管理器
  AgentToolManager get toolManager => _toolManager;

  /// 事件流
  Stream<AgentEvent> get events => _eventController.stream;

  /// 当前执行状态
  bool get isExecuting => _isExecuting;
  bool get isPaused => _isPaused;
  ExecutionPlan? get currentPlan => _currentPlan;

  /// 执行Agent任务
  Future<void> executeTask({
    required String userInstruction,
    required String novelTitle,
    required String genres,
    required int currentChapterCount,
    required Map<String, dynamic> novelInfo,
    required List<Map<String, dynamic>> chapterOverview,
  }) async {
    if (_isExecuting) {
      throw Exception('Agent正在执行其他任务');
    }

    try {
      _isExecuting = true;
      _eventController.add(AgentStarted(userInstruction));

      // 第一阶段：任务理解
      final taskAnalysis = await _analyzeTask(
        userInstruction: userInstruction,
        novelTitle: novelTitle,
        genres: genres,
        currentChapterCount: currentChapterCount,
        novelInfo: novelInfo,
        chapterOverview: chapterOverview,
      );

      // 第二阶段：执行规划
      final executionPlan = await _createExecutionPlan(
        taskAnalysis: taskAnalysis,
        novelTitle: novelTitle,
        chapterOverview: chapterOverview,
      );

      _currentPlan = executionPlan;

      // 第三阶段：执行计划
      await _executePlan(executionPlan);

      _eventController.add(AgentCompleted(executionPlan));
    } catch (e) {
      _eventController.add(AgentFailed(e.toString()));
    } finally {
      _isExecuting = false;
      _isPaused = false;
    }
  }

  /// 暂停执行
  void pauseExecution(String reason) {
    if (_isExecuting && !_isPaused) {
      _isPaused = true;
      _eventController.add(AgentPaused(reason));
    }
  }

  /// 恢复执行
  void resumeExecution() {
    if (_isExecuting && _isPaused) {
      _isPaused = false;
    }
  }

  /// 取消执行
  void cancelExecution() {
    if (_isExecuting) {
      _isExecuting = false;
      _isPaused = false;
      if (_currentPlan != null) {
        _currentPlan = _currentPlan!.copyWith(status: ExecutionStatus.cancelled);
      }
    }
  }

  /// 回滚到检查点
  Future<void> rollbackToCheckpoint(int stepId) async {
    if (_currentPlan == null) return;

    final rollbackPoint = _currentPlan!.rollbackPoints
        .where((point) => point.stepId == stepId)
        .firstOrNull;

    if (rollbackPoint != null) {
      // 实现回滚逻辑
      // 这里应该恢复到指定检查点的状态
      _currentPlan = _currentPlan!.copyWith(
        currentStepIndex: stepId,
        status: ExecutionStatus.paused,
      );
    }
  }

  /// 处理用户确认
  void handleUserConfirmation(int stepId, bool confirmed) {
    if (_currentPlan == null) return;

    final confirmations = _currentPlan!.userConfirmationsNeeded
        .map((conf) => conf.stepId == stepId 
            ? conf.copyWith(userResponse: confirmed)
            : conf)
        .toList();

    _currentPlan = _currentPlan!.copyWith(
      userConfirmationsNeeded: confirmations,
    );

    // 如果确认通过，恢复执行
    if (confirmed && _isPaused) {
      resumeExecution();
    }
  }

  /// 分析任务
  Future<TaskAnalysis> _analyzeTask({
    required String userInstruction,
    required String novelTitle,
    required String genres,
    required int currentChapterCount,
    required Map<String, dynamic> novelInfo,
    required List<Map<String, dynamic>> chapterOverview,
  }) async {
    final prompt = NovelPromptTemplates.agentUnderstandingTemplate.format({
      'novelTitle': novelTitle,
      'genres': genres,
      'currentChapterCount': currentChapterCount.toString(),
      'userInstruction': userInstruction,
      'novelInfo': jsonEncode(novelInfo),
      'chapterOverview': jsonEncode(chapterOverview),
    });

    final response = await _llm.invoke(PromptValue.string(prompt));
    final responseText = response.output.content;

    try {
      final jsonData = jsonDecode(responseText);
      return TaskAnalysis.fromJson(jsonData);
    } catch (e) {
      throw Exception('任务分析失败: 无法解析AI响应');
    }
  }

  /// 创建执行计划
  Future<ExecutionPlan> _createExecutionPlan({
    required TaskAnalysis taskAnalysis,
    required String novelTitle,
    required List<Map<String, dynamic>> chapterOverview,
  }) async {
    final prompt = NovelPromptTemplates.agentPlanningTemplate.format({
      'taskAnalysis': jsonEncode(taskAnalysis.toJson()),
      'novelTitle': novelTitle,
      'existingChapters': jsonEncode(chapterOverview),
      'latestChapterContent': '', // 这里应该传入最新章节内容
    });

    final response = await _llm.invoke(PromptValue.string(prompt));
    final responseText = response.output.content;

    try {
      final jsonData = jsonDecode(responseText);
      return ExecutionPlan.fromJson(jsonData);
    } catch (e) {
      throw Exception('执行计划创建失败: 无法解析AI响应');
    }
  }

  /// 执行计划
  Future<void> _executePlan(ExecutionPlan plan) async {
    for (int i = plan.currentStepIndex; i < plan.executionSteps.length; i++) {
      // 检查是否暂停
      while (_isPaused) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // 检查是否取消
      if (!_isExecuting) {
        break;
      }

      final step = plan.executionSteps[i];
      
      // 检查是否需要用户确认
      final confirmation = plan.userConfirmationsNeeded
          .where((conf) => conf.stepId == step.stepId)
          .firstOrNull;

      if (confirmation != null && confirmation.userResponse == null) {
        _eventController.add(UserConfirmationRequired(confirmation));
        pauseExecution('等待用户确认');
        
        // 等待用户确认
        while (confirmation.userResponse == null && _isPaused) {
          await Future.delayed(const Duration(milliseconds: 100));
        }

        if (confirmation.userResponse == false) {
          // 用户拒绝，跳过此步骤或停止执行
          continue;
        }
      }

      // 执行步骤
      await _executeStep(step);

      // 更新当前步骤索引
      _currentPlan = plan.copyWith(currentStepIndex: i + 1);
    }
  }

  /// 执行单个步骤
  Future<void> _executeStep(ExecutionStep step) async {
    try {
      _eventController.add(AgentStepStarted(step));

      // 更新步骤状态
      final updatedStep = step.copyWith(status: ExecutionStatus.running);
      
      // 执行工具
      final result = await _toolManager.executeTool(
        step.toolRequired,
        step.inputData,
      );

      if (result.success) {
        final completedStep = updatedStep.copyWith(
          status: ExecutionStatus.completed,
          result: jsonEncode(result.data),
        );
        _eventController.add(AgentStepCompleted(completedStep, result));
      } else {
        final failedStep = updatedStep.copyWith(
          status: ExecutionStatus.failed,
          error: result.error,
        );
        _eventController.add(AgentStepFailed(failedStep, result.error ?? '未知错误'));
      }
    } catch (e) {
      final failedStep = step.copyWith(
        status: ExecutionStatus.failed,
        error: e.toString(),
      );
      _eventController.add(AgentStepFailed(failedStep, e.toString()));
    }
  }

  /// 释放资源
  void dispose() {
    _eventController.close();
  }
}
