import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/chat_message.dart' as chat_model;
import 'package:novel_app/models/chat_session.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/screens/ai_chat/chat_sessions_screen.dart';
import 'dart:convert' show jsonEncode, jsonDecode, utf8, LineSplitter;
import 'package:http/http.dart' as http;
import 'package:novel_app/models/model_config.dart' as model_config;
import 'package:novel_app/features/ai_agent/providers/agent_provider.dart';
import 'package:novel_app/features/ai_agent/widgets/agent_mode_switch.dart';
import 'package:novel_app/features/ai_agent/widgets/agent_confirmation_dialog.dart';
import 'package:novel_app/features/ai_agent/widgets/agent_execution_panel.dart';
import 'package:novel_app/features/ai_agent/models/agent_models.dart';
import 'package:novel_app/features/novel_generation/models/novel_models.dart';

class ChatMessage {
  String content; // 改为非final，允许更新内容
  final bool isUser;
  final DateTime timestamp;
  final bool isError;

  ChatMessage({
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.isError = false,
  });
}

class DaizongAIController extends GetxController {
  final NovelController _novelController = Get.find<NovelController>();
  final ApiConfigController _apiConfigController =
      Get.find<ApiConfigController>();
  final ChatHistoryService _chatHistoryService = Get.find<ChatHistoryService>();

  final RxBool isGenerating = false.obs;
  final RxList<ChatMessage> messages = <ChatMessage>[].obs;
  final TextEditingController textController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  // 当前聊天模式: 'normal' 或 'novel'
  final RxString chatMode = 'normal'.obs;

  // 当前选择的小说（用于小说对话模式）
  final Rx<Novel?> selectedNovel = Rx<Novel?>(null);

  // 当前会话
  final Rx<ChatSession?> currentSession = Rx<ChatSession?>(null);

  // 所有会话
  final RxList<ChatSession> allSessions = <ChatSession>[].obs;

  // 聊天上下文
  final List<Map<String, String>> _chatContext = [];

  // HTTP客户端
  final http.Client _client = http.Client();

  @override
  void onInit() {
    super.onInit();

    // 加载所有会话
    _loadSessions();

    // 创建新会话或加载最近的会话
    _createOrLoadSession();
  }

  /// 加载所有会话
  void _loadSessions() {
    try {
      print('[DaizongAIController] 开始加载会话列表');

      // 从ChatHistoryService获取所有会话
      final sessions = _chatHistoryService.sessions;
      print('[DaizongAIController] 从服务获取到 ${sessions.length} 个会话');

      // 更新会话列表
      allSessions.assignAll(sessions);
      print('[DaizongAIController] 已加载 ${allSessions.length} 个会话');
    } catch (e) {
      print('[DaizongAIController] 加载会话失败: $e');
      // 清空会话列表，避免显示错误数据
      allSessions.clear();
    }
  }

  /// 创建新会话或加载最近的会话
  Future<void> _createOrLoadSession() async {
    try {
      // 如果有会话，加载最近的会话
      if (allSessions.isNotEmpty) {
        try {
          await loadSession(allSessions.first);
        } catch (e) {
          print('[DaizongAIController] 加载会话失败，将创建新会话: $e');
          // 如果加载失败，尝试创建新会话
          await createNewSession('新对话');
        }
      } else {
        // 否则创建新会话
        await createNewSession('新对话');
      }
    } catch (e) {
      print('[DaizongAIController] 创建或加载会话失败: $e');

      // 如果加载失败，创建临时会话（不保存）
      _initializeTemporarySession();
    }
  }

  /// 初始化临时会话（不保存到数据库）
  void _initializeTemporarySession() {
    // 添加欢迎消息
    messages.add(ChatMessage(
      content: '您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？',
      isUser: false,
      timestamp: DateTime.now(),
    ));

    // 初始化聊天上下文
    _chatContext.clear();
    _chatContext.add({
      "role": "system",
      "content":
          "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
    });

    _chatContext.add({
      "role": "assistant",
      "content": "您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？"
    });
  }

  @override
  void onClose() {
    textController.dispose();
    scrollController.dispose();
    _client.close();
    super.onClose();
  }

  /// 创建新会话
  Future<void> createNewSession(String title) async {
    try {
      // 创建新的普通聊天会话
      final session = await _chatHistoryService.createNormalChatSession(title);

      // 设置当前会话
      currentSession.value = session;

      // 清空消息列表和聊天上下文
      messages.clear();
      _chatContext.clear();

      // 添加欢迎消息
      final welcomeMessage = ChatMessage(
        content: '您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？',
        isUser: false,
        timestamp: DateTime.now(),
      );
      messages.add(welcomeMessage);

      // 初始化聊天上下文
      _chatContext.add({
        "role": "system",
        "content":
            "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
      });

      _chatContext.add({
        "role": "assistant",
        "content":
            "您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？"
      });

      // 保存欢迎消息到数据库
      try {
        await _chatHistoryService.addAIReply(
          welcomeMessage.content,
          sessionId: session.id,
        );
      } catch (e) {
        print('[DaizongAIController] 保存欢迎消息失败，但会话已创建: $e');
      }

      // 重新加载会话列表
      _loadSessions();

      print('[DaizongAIController] 已创建新会话: ${session.title}');
    } catch (e) {
      print('[DaizongAIController] 创建新会话失败: $e');
      // 如果创建会话失败，创建临时会话
      _initializeTemporarySession();
    }
  }

  /// 加载会话
  Future<void> loadSession(ChatSession session) async {
    try {
      // 设置当前会话
      currentSession.value = session;

      // 加载会话历史
      await _chatHistoryService.loadSessionHistory(session.id);

      // 将ChatHistoryService中的消息转换为本地消息格式
      final convertedMessages = _chatHistoryService.messages
          .map((msg) => ChatMessage(
                content: msg.content,
                isUser: msg.type == chat_model.ChatMessageType.user,
                timestamp: msg.timestamp,
                isError: false,
              ))
          .toList();

      // 更新消息列表
      messages.assignAll(convertedMessages);

      // 重建聊天上下文
      _rebuildChatContext();

      // 如果消息为空，添加欢迎消息
      if (messages.isEmpty) {
        final welcomeMessage = ChatMessage(
          content:
              '您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？',
          isUser: false,
          timestamp: DateTime.now(),
        );
        messages.add(welcomeMessage);

        // 保存欢迎消息到数据库
        await _chatHistoryService.addAIReply(
          welcomeMessage.content,
          sessionId: session.id,
        );
      }

      // 滚动到底部
      _scrollToBottom();

      print('[DaizongAIController] 已加载会话: ${session.title}');
    } catch (e) {
      print('[DaizongAIController] 加载会话失败: $e');

      // 如果加载失败，创建临时会话
      _initializeTemporarySession();
    }
  }

  /// 删除会话
  Future<void> deleteSession(ChatSession session) async {
    try {
      // 删除会话
      await _chatHistoryService.deleteSession(session.id);

      // 重新加载会话列表
      _loadSessions();

      // 如果删除的是当前会话，创建新会话
      if (currentSession.value?.id == session.id) {
        if (allSessions.isNotEmpty) {
          await loadSession(allSessions.first);
        } else {
          await createNewSession('新对话');
        }
      }

      print('[DaizongAIController] 已删除会话: ${session.title}');
    } catch (e) {
      print('[DaizongAIController] 删除会话失败: $e');
    }
  }

  /// 重建聊天上下文
  void _rebuildChatContext() {
    _chatContext.clear();

    // 添加系统消息
    _chatContext.add({
      "role": "system",
      "content":
          "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
    });

    // 添加历史消息（最多20条）
    final historyMessages = messages.length > 20
        ? messages.sublist(messages.length - 20)
        : messages;

    for (final msg in historyMessages) {
      _chatContext.add({
        "role": msg.isUser ? "user" : "assistant",
        "content": msg.content,
      });
    }
  }

  // 切换聊天模式
  void switchChatMode(String mode) {
    if (mode != chatMode.value) {
      chatMode.value = mode;
      if (mode == 'normal') {
        messages.add(ChatMessage(
          content: '已切换到普通聊天模式。我可以帮助您进行小说创作、角色设计、情节构思等工作。',
          isUser: false,
          timestamp: DateTime.now(),
        ));

        // 重置聊天上下文
        _chatContext.clear();
        _chatContext.add({
          "role": "system",
          "content":
              "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
        });
        _chatContext.add({
          "role": "assistant",
          "content": "已切换到普通聊天模式。我可以帮助您进行小说创作、角色设计、情节构思等工作。"
        });
      } else {
        messages.add(ChatMessage(
          content: '已切换到小说对话模式。请选择一部小说进行对话。',
          isUser: false,
          timestamp: DateTime.now(),
        ));

        // 重置聊天上下文
        _chatContext.clear();
        _chatContext.add({
          "role": "system",
          "content":
              "你是岱宗AI，一个专业的小说创作助手。你现在处于小说对话模式，可以帮助用户分析小说中的角色、情节、主题等内容，并提供创作建议。回答要专业、有创意且符合中文写作习惯。"
        });
        _chatContext
            .add({"role": "assistant", "content": "已切换到小说对话模式。请选择一部小说进行对话。"});
      }

      _scrollToBottom();
    }
  }

  // 选择小说
  void selectNovel(Novel novel) {
    selectedNovel.value = novel;

    // 构建小说信息摘要
    String novelSummary = '小说标题：${novel.title}\n';
    novelSummary += '类型：${novel.genre}\n';
    if (novel.outline.isNotEmpty) {
      novelSummary += '大纲：${novel.outline}\n';
    }

    // 添加系统消息
    messages.add(ChatMessage(
      content: '已选择《${novel.title}》。您可以就这部小说的情节、角色、主题等方面与我进行讨论。',
      isUser: false,
      timestamp: DateTime.now(),
    ));

    // 更新聊天上下文
    _chatContext.clear();
    _chatContext.add({
      "role": "system",
      "content":
          "你是岱宗AI，一个专业的小说创作助手。用户已选择了小说《${novel.title}》进行对话。以下是小说的相关信息：\n$novelSummary\n请基于这些信息与用户进行对话，帮助用户分析和改进小说。回答要专业、有创意且符合中文写作习惯。"
    });
    _chatContext.add({
      "role": "assistant",
      "content": "已选择《${novel.title}》。您可以就这部小说的情节、角色、主题等方面与我进行讨论。"
    });

    _scrollToBottom();
  }

  // 发送消息并与AI交互
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || isGenerating.value) return;

    // 检查是否为Agent模式的任务指令
    if (_isAgentTaskInstruction(content)) {
      await _handleAgentTask(content);
      return;
    }

    // 清空输入框
    textController.clear();

    // 添加用户消息
    final userMessage = ChatMessage(
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
    );
    messages.add(userMessage);

    // 更新聊天上下文
    _chatContext.add({"role": "user", "content": content});

    // 滚动到底部
    _scrollToBottom();

    // 设置生成状态
    isGenerating.value = true;

    // 如果有当前会话，保存用户消息到数据库
    if (currentSession.value != null) {
      try {
        await _chatHistoryService.continueChat(
          content,
        );
      } catch (e) {
        print('[DaizongAIController] 保存用户消息失败: $e');
      }
    }

    try {
      // 获取当前模型配置
      final modelConfig = Get.find<ApiConfigController>().getCurrentModel();

      // 检查是否是通义千问模型
      final isQwen = modelConfig.name.toLowerCase().contains('qwen') ||
          modelConfig.model.toLowerCase().contains('qwen');

      // 对于通义千问模型，强制使用流式输出
      final useStream = isQwen || modelConfig.name.contains('通义');

      if (useStream) {
        // 使用流式输出模式
        await _sendStreamRequest(content, modelConfig);
      } else {
        // 使用普通请求模式
        await _sendNormalRequest(content, modelConfig);
      }
    } catch (e) {
      // 处理异常
      final errorMessage = ChatMessage(
        content: '发生错误: $e',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      );
      messages.add(errorMessage);

      // 如果有当前会话，保存错误消息到数据库
      if (currentSession.value != null) {
        try {
          await _chatHistoryService.addAIReply(
            errorMessage.content,
          );
        } catch (e) {
          print('[DaizongAIController] 保存错误消息失败: $e');
        }
      }
    } finally {
      isGenerating.value = false;
      _scrollToBottom();
    }
  }

  // 使用流式输出模式发送请求
  Future<void> _sendStreamRequest(String content, dynamic modelConfig) async {
    try {
      // 准备请求数据
      final Map<String, dynamic> requestData = {};
      final Map<String, String> headers = {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'text/event-stream',
      };

      // 根据不同的API格式构建请求
      if (modelConfig.apiFormat == 'OpenAI API兼容') {
        // OpenAI兼容格式
        requestData['model'] = modelConfig.model;
        requestData['messages'] = _chatContext;
        requestData['temperature'] = modelConfig.temperature;
        requestData['max_tokens'] = modelConfig.maxTokens;
        requestData['top_p'] = modelConfig.topP;
        requestData['stream'] = true; // 启用流式输出

        if (modelConfig.apiKey.isNotEmpty) {
          headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
        }

        // 如果是百度千帆，添加appId
        if (modelConfig.name.contains('百度') && modelConfig.appId.isNotEmpty) {
          headers['X-Bce-App-Id'] = modelConfig.appId;
        }
      } else if (modelConfig.apiFormat == 'Google API') {
        // Google API格式 - 不支持system角色，需要特殊处理
        List<Map<String, dynamic>> contents = [];
        String combinedContent = '';

        // 合并所有消息内容，因为Google API不支持多角色对话
        for (var msg in _chatContext) {
          if (msg['role'] == 'system') {
            // 将系统消息作为前缀
            combinedContent = '${msg['content']}\n\n$combinedContent';
          } else if (msg['role'] == 'user') {
            combinedContent += 'User: ${msg['content']}\n';
          } else if (msg['role'] == 'assistant') {
            combinedContent += 'Assistant: ${msg['content']}\n';
          }
        }

        // 创建单个用户消息
        contents.add({
          'parts': [
            {'text': combinedContent.trim()}
          ]
        });

        requestData['contents'] = contents;
        requestData['generationConfig'] = {
          'temperature': modelConfig.temperature,
          'topP': modelConfig.topP,
          'maxOutputTokens': modelConfig.maxTokens,
        };
        // Google API不支持stream参数，流式处理通过streamGenerateContent端点实现

        if (modelConfig.apiKey.isNotEmpty) {
          headers['x-goog-api-key'] = modelConfig.apiKey;
        }
      } else {
        // 默认使用OpenAI格式
        requestData['model'] = modelConfig.model;
        requestData['messages'] = _chatContext;
        requestData['temperature'] = modelConfig.temperature;
        requestData['max_tokens'] = modelConfig.maxTokens;
        requestData['stream'] = true; // 启用流式输出

        if (modelConfig.apiKey.isNotEmpty) {
          headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
        }
      }

      // 发送请求
      final url = '${modelConfig.apiUrl}${modelConfig.apiPath}';
      final request = http.Request('POST', Uri.parse(url));
      request.headers.addAll(headers);
      request.body = jsonEncode(requestData);

      final streamedResponse = await _client.send(request);

      if (streamedResponse.statusCode != 200) {
        // 处理错误
        final response = await http.Response.fromStream(streamedResponse);
        String errorBody;
        try {
          errorBody = utf8.decode(response.bodyBytes);
        } catch (e) {
          errorBody = response.body;
        }

        messages.add(ChatMessage(
          content: '请求失败，状态码: ${response.statusCode}\n$errorBody',
          isUser: false,
          timestamp: DateTime.now(),
          isError: true,
        ));
        return;
      }

      // 创建一个空的AI消息
      final aiMessage = ChatMessage(
        content: '',
        isUser: false,
        timestamp: DateTime.now(),
      );
      messages.add(aiMessage);

      // 处理流式响应
      final stream = streamedResponse.stream
          .transform(utf8.decoder)
          .transform(const LineSplitter());

      await for (var line in stream) {
        if (line.trim().isEmpty) continue;
        if (line.startsWith('data: ')) {
          line = line.substring(6);
          if (line == '[DONE]') break;

          try {
            final data = jsonDecode(line);
            String chunk = '';

            // 根据不同的API格式解析响应
            if (modelConfig.apiFormat == 'OpenAI API兼容') {
              if (data['choices'] != null &&
                  data['choices'][0]['delta'] != null &&
                  data['choices'][0]['delta']['content'] != null) {
                chunk = data['choices'][0]['delta']['content'];
              }
            } else if (modelConfig.apiFormat == 'Google API') {
              if (data['candidates'] != null &&
                  data['candidates'][0]['content'] != null &&
                  data['candidates'][0]['content']['parts'] != null &&
                  data['candidates'][0]['content']['parts'][0]['text'] !=
                      null) {
                chunk = data['candidates'][0]['content']['parts'][0]['text'];
              }
            }

            if (chunk.isNotEmpty) {
              // 更新AI消息内容
              aiMessage.content += chunk;
              // 强制UI更新
              messages.refresh();
              // 滚动到底部
              _scrollToBottom();
            }
          } catch (e) {
            print('解析流式响应出错: $e');
          }
        }
      }

      // 更新聊天上下文
      _chatContext.add({"role": "assistant", "content": aiMessage.content});

      // 如果有当前会话，保存AI回复到数据库
      if (currentSession.value != null) {
        try {
          await _chatHistoryService.addAIReply(
            aiMessage.content,
          );
        } catch (e) {
          print('[DaizongAIController] 保存AI回复失败: $e');
        }
      }
    } catch (e) {
      messages.add(ChatMessage(
        content: '流式请求失败: $e',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      ));
    }
  }

  // 使用普通模式发送请求
  Future<void> _sendNormalRequest(String content, dynamic modelConfig) async {
    // 准备请求数据
    final Map<String, dynamic> requestData = {};
    final Map<String, String> headers = {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json; charset=utf-8',
    };

    // 根据不同的API格式构建请求
    if (modelConfig.apiFormat == 'OpenAI API兼容') {
      // OpenAI兼容格式
      requestData['model'] = modelConfig.model;
      requestData['messages'] = _chatContext;
      requestData['temperature'] = modelConfig.temperature;
      requestData['max_tokens'] = modelConfig.maxTokens;
      requestData['top_p'] = modelConfig.topP;
      requestData['stream'] = false;

      if (modelConfig.apiKey.isNotEmpty) {
        headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
      }

      // 如果是百度千帆，添加appId
      if (modelConfig.name.contains('百度') && modelConfig.appId.isNotEmpty) {
        headers['X-Bce-App-Id'] = modelConfig.appId;
      }
    } else if (modelConfig.apiFormat == 'Google API') {
      // Google API格式 - 不支持system角色，需要特殊处理
      List<Map<String, dynamic>> contents = [];
      String combinedContent = '';

      // 合并所有消息内容，因为Google API不支持多角色对话
      for (var msg in _chatContext) {
        if (msg['role'] == 'system') {
          // 将系统消息作为前缀
          combinedContent = '${msg['content']}\n\n$combinedContent';
        } else if (msg['role'] == 'user') {
          combinedContent += 'User: ${msg['content']}\n';
        } else if (msg['role'] == 'assistant') {
          combinedContent += 'Assistant: ${msg['content']}\n';
        }
      }

      // 创建单个用户消息
      contents.add({
        'parts': [
          {'text': combinedContent.trim()}
        ]
      });

      requestData['contents'] = contents;
      requestData['generationConfig'] = {
        'temperature': modelConfig.temperature,
        'topP': modelConfig.topP,
        'maxOutputTokens': modelConfig.maxTokens,
      };

      if (modelConfig.apiKey.isNotEmpty) {
        headers['x-goog-api-key'] = modelConfig.apiKey;
      }
    } else {
      // 默认使用OpenAI格式
      requestData['model'] = modelConfig.model;
      requestData['messages'] = _chatContext;
      requestData['temperature'] = modelConfig.temperature;
      requestData['max_tokens'] = modelConfig.maxTokens;

      if (modelConfig.apiKey.isNotEmpty) {
        headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
      }
    }

    // 发送请求
    final url = '${modelConfig.apiUrl}${modelConfig.apiPath}';
    final response = await _client
        .post(
          Uri.parse(url),
          headers: headers,
          body: jsonEncode(requestData),
        )
        .timeout(Duration(seconds: modelConfig.timeout));

    // 处理响应
    if (response.statusCode == 200) {
      // 确保使用UTF-8解码响应内容
      final responseData = jsonDecode(utf8.decode(response.bodyBytes));
      String aiResponse = '';

      // 根据不同的API格式解析响应
      if (modelConfig.apiFormat == 'OpenAI API兼容') {
        aiResponse = responseData['choices'][0]['message']['content'];
      } else if (modelConfig.apiFormat == 'Google API') {
        aiResponse =
            responseData['candidates'][0]['content']['parts'][0]['text'];
      } else {
        // 默认尝试OpenAI格式
        aiResponse = responseData['choices'][0]['message']['content'];
      }

      // 添加AI响应到消息列表
      final aiMessage = ChatMessage(
        content: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
      );
      messages.add(aiMessage);

      // 更新聊天上下文
      _chatContext.add({"role": "assistant", "content": aiResponse});

      // 如果有当前会话，保存AI回复到数据库
      if (currentSession.value != null) {
        try {
          await _chatHistoryService.addAIReply(
            aiResponse,
          );
        } catch (e) {
          print('[DaizongAIController] 保存AI回复失败: $e');
        }
      }
    } else {
      // 处理错误
      String errorBody;
      try {
        // 尝试使用UTF-8解码错误消息
        errorBody = utf8.decode(response.bodyBytes);
      } catch (e) {
        // 如果解码失败，使用原始消息
        errorBody = response.body;
      }

      final errorMessage = ChatMessage(
        content: '请求失败，状态码: ${response.statusCode}\n$errorBody',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      );
      messages.add(errorMessage);
    }
  }

  // 滚动到底部
  void _scrollToBottom() {
    if (scrollController.hasClients) {
      Future.delayed(const Duration(milliseconds: 100), () {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  /// 检查是否为Agent任务指令
  bool _isAgentTaskInstruction(String content) {
    final agentKeywords = [
      '创建章节', '生成章节', '写章节', '新建章节',
      '编辑章节', '修改章节', '更新章节',
      '分析情节', '角色发展', '世界观',
      '批量创建', '批量生成', '一次性创建',
    ];

    return agentKeywords.any((keyword) => content.contains(keyword));
  }

  /// 处理Agent任务
  Future<void> _handleAgentTask(String instruction) async {
    try {
      // 清空输入框
      textController.clear();

      // 添加用户消息
      final userMessage = ChatMessage(
        content: instruction,
        isUser: true,
        timestamp: DateTime.now(),
      );
      messages.add(userMessage);

      // 添加Agent处理提示消息
      final agentMessage = ChatMessage(
        content: '正在启动Agent模式处理您的请求...',
        isUser: false,
        timestamp: DateTime.now(),
      );
      messages.add(agentMessage);

      _scrollToBottom();

      // 获取AgentProvider并执行任务
      // 注意：这里需要从Provider.of获取，因为AgentProvider是通过ChangeNotifierProvider提供的
      // final agentProvider = Get.find<AgentProvider>();
      // 暂时跳过Agent执行，直接返回提示消息

      // 添加Agent模式提示消息
      final agentResponseMessage = ChatMessage(
        content: 'Agent模式已启动，正在分析您的请求...\n\n'
                '检测到的任务类型：${_getTaskType(instruction)}\n'
                '建议操作：${_getSuggestions(instruction)}\n\n'
                '注意：Agent模式功能正在开发中，当前为演示版本。',
        isUser: false,
        timestamp: DateTime.now(),
      );

      // 移除之前的处理提示消息，添加新的响应消息
      if (messages.isNotEmpty && messages.last.content.contains('正在启动Agent模式')) {
        messages.removeLast();
      }
      messages.add(agentResponseMessage);

    } catch (e) {
      final errorMessage = ChatMessage(
        content: 'Agent任务执行失败: $e',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      );
      messages.add(errorMessage);
      _scrollToBottom();
    }
  }

  /// 获取任务类型
  String _getTaskType(String instruction) {
    if (instruction.contains('创建') || instruction.contains('生成') || instruction.contains('写')) {
      return '章节创建任务';
    } else if (instruction.contains('编辑') || instruction.contains('修改') || instruction.contains('更新')) {
      return '章节编辑任务';
    } else if (instruction.contains('分析') || instruction.contains('情节')) {
      return '情节分析任务';
    } else if (instruction.contains('角色') || instruction.contains('人物')) {
      return '角色发展任务';
    } else if (instruction.contains('世界观') || instruction.contains('设定')) {
      return '世界观构建任务';
    } else {
      return '综合创作任务';
    }
  }

  /// 获取建议操作
  String _getSuggestions(String instruction) {
    if (instruction.contains('创建') || instruction.contains('生成')) {
      return '建议先确定章节大纲，然后逐步创建内容';
    } else if (instruction.contains('编辑') || instruction.contains('修改')) {
      return '建议先分析现有内容，然后提出具体修改方案';
    } else if (instruction.contains('批量')) {
      return '建议分步骤执行，确保每个章节的质量';
    } else {
      return '建议明确具体需求，以便提供更精准的帮助';
    }
  }
}

class DaizongAIScreen extends StatelessWidget {
  const DaizongAIScreen({super.key});

  // 定义主题色
  static const Color earthYellow = Color(0xFFD4B483);
  static const Color forestGreen = Color(0xFF3A6B35);
  static const Color lightGreen = Color(0xFF93B884);
  static const Color lightYellow = Color(0xFFF2E3BC);

  /// 显示会话列表页面
  Future<void> _showSessionsScreen(BuildContext context) async {
    // 导入会话列表页面
    final ChatSession? selectedSession =
        await Get.to(() => const ChatSessionsScreen());

    // 如果用户选择了会话，加载该会话
    if (selectedSession != null) {
      final controller = Get.find<DaizongAIController>();
      await controller.loadSession(selectedSession);
    }
  }

  /// 创建新会话
  Future<void> _createNewSession(BuildContext context) async {
    // 显示对话框，让用户输入会话名称
    final TextEditingController nameController = TextEditingController();

    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建新会话'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            hintText: '请输入会话名称',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('创建'),
          ),
        ],
      ),
    );

    if (confirmed == true && nameController.text.trim().isNotEmpty) {
      final controller = Get.find<DaizongAIController>();
      await controller.createNewSession(nameController.text.trim());
    }

    nameController.dispose();
  }

  // 处理Agent确认事件
  void _handleAgentConfirmation(BuildContext context, UserConfirmation confirmation) {
    AgentConfirmationDialog.show(
      context,
      confirmation,
      onConfirmed: () {
        // 确认后的处理逻辑
      },
      onRejected: () {
        // 拒绝后的处理逻辑
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DaizongAIController());
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // 初始化AgentProvider
    final agentProvider = Get.put(AgentProvider(
      apiConfigController: Get.find<ApiConfigController>(),
    ));

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Text('岱宗AI'),
            const SizedBox(width: 16),
            // Agent状态指示器
            const AgentStatusIndicator(),
          ],
        ),
        backgroundColor: forestGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Agent模式切换
          const AgentModeSwitch(),
          const SizedBox(width: 8),
          // 会话管理按钮
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _showSessionsScreen(context),
            tooltip: '会话历史',
          ),
          // 新建会话按钮
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _createNewSession(context),
            tooltip: '新建会话',
          ),
        ],
      ),
      body: Column(
        children: [
          // Agent进度指示器
          const AgentProgressIndicator(),

          // 模式选择器
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              color: isDark ? Colors.grey.shade800 : lightYellow,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Obx(() => Expanded(
                      child: SegmentedButton<String>(
                        segments: const [
                          ButtonSegment<String>(
                            value: 'normal',
                            label: Text('普通聊天'),
                            icon: Icon(Icons.chat_bubble_outline),
                          ),
                          ButtonSegment<String>(
                            value: 'novel',
                            label: Text('小说对话'),
                            icon: Icon(Icons.book_outlined),
                          ),
                        ],
                        selected: {controller.chatMode.value},
                        onSelectionChanged: (Set<String> selection) {
                          controller.switchChatMode(selection.first);
                        },
                        style: ButtonStyle(
                          backgroundColor:
                              WidgetStateProperty.resolveWith<Color>(
                            (states) {
                              if (states.contains(WidgetState.selected)) {
                                return forestGreen;
                              }
                              return isDark
                                  ? Colors.grey.shade700
                                  : Colors.white;
                            },
                          ),
                          foregroundColor:
                              WidgetStateProperty.resolveWith<Color>(
                            (states) {
                              if (states.contains(WidgetState.selected)) {
                                return Colors.white;
                              }
                              return isDark ? Colors.white : Colors.black87;
                            },
                          ),
                        ),
                      ),
                    )),

                // 小说选择器（仅在小说对话模式下显示）
                Obx(
                  () => controller.chatMode.value == 'novel'
                      ? Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: _buildNovelSelector(context, controller),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),

          // 消息列表
          Expanded(
            child: Container(
              color: isDark ? Colors.grey.shade900 : lightYellow.withAlpha(51),
              child: Column(
                children: [
                  // Agent执行面板
                  const AgentExecutionPanel(),

                  // 消息列表
                  Expanded(
                    child: Obx(() => ListView.builder(
                          controller: controller.scrollController,
                          padding:
                              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          itemCount: controller.messages.length,
                          itemBuilder: (context, index) {
                            final message = controller.messages[index];
                            return _buildMessageBubble(context, message, isDark);
                          },
                        )),
                  ),
                ],
              ),
            ),
          ),

          // 输入框区域
          Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey.shade800 : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.textController,
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    decoration: InputDecoration(
                      hintText: '输入消息...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: isDark
                          ? Colors.grey.shade700
                          : lightYellow.withAlpha(77),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onSubmitted: (value) {
                      if (value.trim().isNotEmpty) {
                        controller.sendMessage(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Obx(() => Material(
                      color: forestGreen,
                      borderRadius: BorderRadius.circular(24),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(24),
                        onTap: controller.isGenerating.value
                            ? null
                            : () => controller
                                .sendMessage(controller.textController.text),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          child: controller.isGenerating.value
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(
                                  Icons.send,
                                  color: Colors.white,
                                ),
                        ),
                      ),
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNovelSelector(
      BuildContext context, DaizongAIController controller) {
    final novels = Get.find<NovelController>().novels;

    return DropdownButton<Novel>(
      value: controller.selectedNovel.value,
      hint: const Text('选择小说'),
      underline: Container(height: 0),
      icon: const Icon(Icons.arrow_drop_down),
      iconEnabledColor: forestGreen,
      dropdownColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF2A2A2A)
          : Colors.white,
      onChanged: (Novel? novel) {
        if (novel != null) {
          controller.selectNovel(novel);
        }
      },
      items: novels.map<DropdownMenuItem<Novel>>((Novel novel) {
        return DropdownMenuItem<Novel>(
          value: novel,
          child: SizedBox(
            width: 100,
            child: Text(
              novel.title,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMessageBubble(
      BuildContext context, ChatMessage message, bool isDark) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: message.isUser
              ? forestGreen
              : message.isError
                  ? Colors.red.shade100
                  : (isDark
                      ? Colors.grey.shade700
                      : earthYellow.withAlpha(179)),
          borderRadius: BorderRadius.circular(18),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.isUser ? '我' : '岱宗AI',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: message.isUser
                    ? Colors.white70
                    : (isDark ? Colors.white70 : Colors.black54),
              ),
            ),
            const SizedBox(height: 4),
            SelectableText(
              message.content,
              style: TextStyle(
                color: message.isUser
                    ? Colors.white
                    : message.isError
                        ? Colors.red.shade900
                        : (isDark ? Colors.white : Colors.black87),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
