/// Agent模式切换组件
library agent_mode_switch;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/agent_models.dart';
import '../providers/agent_provider.dart';

/// Agent模式切换开关
class AgentModeSwitch extends StatelessWidget {
  final VoidCallback? onModeChanged;

  const AgentModeSwitch({
    super.key,
    this.onModeChanged,
  });

  @override
  Widget build(BuildContext context) {
    // 简化的实现，避免GetBuilder的复杂性
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 聊天模式按钮
          _ModeButton(
            icon: Icons.chat_bubble_outline,
            label: '聊天模式',
            isSelected: true, // 默认选中聊天模式
            isEnabled: true,
            onTap: () => _switchMode(context, AgentMode.chat),
          ),
          const SizedBox(width: 8),
          // 分隔线
          Container(
            width: 1,
            height: 20,
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          const SizedBox(width: 8),
          // Agent模式按钮
          _ModeButton(
            icon: Icons.auto_awesome,
            label: 'Agent模式',
            isSelected: false,
            isEnabled: true,
            onTap: () => _switchMode(context, AgentMode.agent),
          ),
        ],
      ),
    );
  }
  }

  void _switchMode(BuildContext context, AgentMode mode) {
    try {
      final agentProvider = Get.find<AgentProvider>(tag: 'library_agent');
      agentProvider.switchMode(mode);
      onModeChanged?.call();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('切换模式失败: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }
}

/// 模式按钮
class _ModeButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final bool isEnabled;
  final VoidCallback onTap;

  const _ModeButton({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.isEnabled,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return InkWell(
      onTap: isEnabled ? onTap : null,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected 
              ? colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isEnabled
                  ? (isSelected ? colorScheme.primary : colorScheme.onSurface)
                  : colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isEnabled
                    ? (isSelected ? colorScheme.primary : colorScheme.onSurface)
                    : colorScheme.onSurface.withOpacity(0.5),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Agent状态指示器
class AgentStatusIndicator extends StatelessWidget {
  const AgentStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AgentProvider>(
      builder: (agentProvider) {
        if (agentProvider.currentMode != AgentMode.agent) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(context, agentProvider),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (agentProvider.isExecuting) ...[
                SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Text(
                agentProvider.getStatusText(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getStatusColor(BuildContext context, AgentProvider agentProvider) {
    final colorScheme = Theme.of(context).colorScheme;
    
    if (agentProvider.lastError != null) {
      return colorScheme.error;
    }
    
    if (agentProvider.isPaused) {
      return Colors.orange;
    }
    
    if (agentProvider.isExecuting) {
      return colorScheme.primary;
    }
    
    return colorScheme.secondary;
  }
}

/// Agent进度指示器
class AgentProgressIndicator extends StatelessWidget {
  const AgentProgressIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AgentProvider>(
      builder: (agentProvider) {
        if (!agentProvider.isExecuting) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 进度条
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: agentProvider.progress,
                      backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    agentProvider.getProgressText(),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 当前步骤描述
              Text(
                agentProvider.currentStepDescription,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              // 控制按钮
              Row(
                children: [
                  if (agentProvider.canPause())
                    _ControlButton(
                      icon: Icons.pause,
                      label: '暂停',
                      onPressed: () => agentProvider.pauseExecution('用户暂停'),
                    ),
                  if (agentProvider.canResume())
                    _ControlButton(
                      icon: Icons.play_arrow,
                      label: '继续',
                      onPressed: agentProvider.resumeExecution,
                    ),
                  if (agentProvider.canCancel()) ...[
                    const SizedBox(width: 8),
                    _ControlButton(
                      icon: Icons.stop,
                      label: '取消',
                      onPressed: agentProvider.cancelExecution,
                      isDestructive: true,
                    ),
                  ],
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

/// 控制按钮
class _ControlButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final bool isDestructive;

  const _ControlButton({
    required this.icon,
    required this.label,
    required this.onPressed,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        size: 16,
        color: isDestructive 
            ? Theme.of(context).colorScheme.error
            : null,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isDestructive 
              ? Theme.of(context).colorScheme.error
              : null,
        ),
      ),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        side: BorderSide(
          color: isDestructive 
              ? Theme.of(context).colorScheme.error
              : Theme.of(context).colorScheme.outline,
        ),
      ),
    );
  }
}
