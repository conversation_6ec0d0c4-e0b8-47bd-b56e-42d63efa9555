/// Agent用户确认对话框
library agent_confirmation_dialog;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/agent_models.dart';
import '../providers/agent_provider.dart';

/// 用户确认对话框
class AgentConfirmationDialog extends StatelessWidget {
  final UserConfirmation confirmation;
  final VoidCallback? onConfirmed;
  final VoidCallback? onRejected;

  const AgentConfirmationDialog({
    super.key,
    required this.confirmation,
    this.onConfirmed,
    this.onRejected,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.help_outline,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Text('需要确认'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '步骤 ${confirmation.stepId}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            confirmation.reason,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '确认后Agent将继续执行，拒绝将跳过此步骤',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            context.read<AgentProvider>().handleUserConfirmation(false);
            onRejected?.call();
          },
          child: const Text('拒绝'),
        ),
        FilledButton(
          onPressed: () {
            Navigator.of(context).pop();
            context.read<AgentProvider>().handleUserConfirmation(true);
            onConfirmed?.call();
          },
          child: const Text('确认'),
        ),
      ],
    );
  }

  /// 显示确认对话框
  static Future<void> show(
    BuildContext context,
    UserConfirmation confirmation, {
    VoidCallback? onConfirmed,
    VoidCallback? onRejected,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AgentConfirmationDialog(
        confirmation: confirmation,
        onConfirmed: onConfirmed,
        onRejected: onRejected,
      ),
    );
  }
}

/// 章节创建确认对话框
class ChapterCreationConfirmationDialog extends StatelessWidget {
  final List<Map<String, dynamic>> chaptersToCreate;
  final VoidCallback? onConfirmed;
  final VoidCallback? onRejected;

  const ChapterCreationConfirmationDialog({
    super.key,
    required this.chaptersToCreate,
    this.onConfirmed,
    this.onRejected,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.create_new_folder,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Text('确认创建章节'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Agent计划创建以下章节：',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: chaptersToCreate.length,
                itemBuilder: (context, index) {
                  final chapter = chaptersToCreate[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.primary,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '第${chapter['chapter_number']}章',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  chapter['chapter_title'] ?? '未命名',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (chapter['summary'] != null) ...[
                            const SizedBox(height: 8),
                            Text(
                              chapter['summary'],
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_outlined,
                    size: 16,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '确认后将创建 ${chaptersToCreate.length} 个新章节',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            onRejected?.call();
          },
          child: const Text('取消'),
        ),
        FilledButton(
          onPressed: () {
            Navigator.of(context).pop();
            onConfirmed?.call();
          },
          child: const Text('确认创建'),
        ),
      ],
    );
  }

  /// 显示章节创建确认对话框
  static Future<bool> show(
    BuildContext context,
    List<Map<String, dynamic>> chaptersToCreate,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ChapterCreationConfirmationDialog(
        chaptersToCreate: chaptersToCreate,
      ),
    );
    return result ?? false;
  }
}

/// Agent错误对话框
class AgentErrorDialog extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;
  final VoidCallback? onCancel;

  const AgentErrorDialog({
    super.key,
    required this.error,
    this.onRetry,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(width: 8),
          const Text('执行错误'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Agent执行过程中发生错误：',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.errorContainer.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              error,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
      actions: [
        if (onCancel != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onCancel?.call();
            },
            child: const Text('取消'),
          ),
        if (onRetry != null)
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry?.call();
            },
            child: const Text('重试'),
          ),
        if (onRetry == null && onCancel == null)
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
      ],
    );
  }

  /// 显示错误对话框
  static Future<void> show(
    BuildContext context,
    String error, {
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) async {
    return showDialog<void>(
      context: context,
      builder: (context) => AgentErrorDialog(
        error: error,
        onRetry: onRetry,
        onCancel: onCancel,
      ),
    );
  }
}
