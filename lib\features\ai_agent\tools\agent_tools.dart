/// Agent工具系统
library agent_tools;

import 'dart:convert';
import 'package:langchain/langchain.dart';
import '../models/agent_models.dart';
import '../services/apply_service.dart';
import '../../novel_generation/models/novel_models.dart';

/// 工具执行结果
class ToolResult {
  final bool success;
  final Map<String, dynamic> data;
  final String? error;
  final List<String> warnings;

  const ToolResult({
    required this.success,
    required this.data,
    this.error,
    this.warnings = const [],
  });

  factory ToolResult.success(Map<String, dynamic> data, {List<String> warnings = const []}) {
    return ToolResult(
      success: true,
      data: data,
      warnings: warnings,
    );
  }

  factory ToolResult.failure(String error) {
    return ToolResult(
      success: false,
      data: {},
      error: error,
    );
  }
}

/// 抽象工具基类
abstract class AgentTool {
  final ToolType toolType;
  final String name;
  final String description;

  const AgentTool({
    required this.toolType,
    required this.name,
    required this.description,
  });

  /// 执行工具
  Future<ToolResult> execute(Map<String, dynamic> parameters);

  /// 验证参数
  bool validateParameters(Map<String, dynamic> parameters);

  /// 获取所需参数列表
  List<String> getRequiredParameters();
}

/// 章节创建工具
class ChapterCreatorTool extends AgentTool {
  final Function(ChapterData) onChapterCreated;

  ChapterCreatorTool({
    required this.onChapterCreated,
  }) : super(
          toolType: ToolType.chapterCreator,
          name: '章节创建工具',
          description: '根据大纲和上下文创建新的章节内容',
        );

  @override
  List<String> getRequiredParameters() {
    return [
      'chapter_number',
      'chapter_title',
      'novel_title',
      'novel_settings',
      'previous_content',
      'outline_info',
    ];
  }

  @override
  bool validateParameters(Map<String, dynamic> parameters) {
    final required = getRequiredParameters();
    return required.every((param) => parameters.containsKey(param));
  }

  @override
  Future<ToolResult> execute(Map<String, dynamic> parameters) async {
    try {
      if (!validateParameters(parameters)) {
        return ToolResult.failure('缺少必需参数');
      }

      final chapterNumber = parameters['chapter_number'] as int;
      final chapterTitle = parameters['chapter_title'] as String;
      final content = parameters['content'] as String;

      // 创建章节数据
      final chapterData = ChapterData(
        chapterNumber: chapterNumber,
        title: chapterTitle,
        content: content,
        wordCount: content.length,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 通知章节创建
      onChapterCreated(chapterData);

      return ToolResult.success({
        'chapter_number': chapterNumber,
        'chapter_title': chapterTitle,
        'content': content,
        'word_count': content.length,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      return ToolResult.failure('章节创建失败: $e');
    }
  }
}

/// 章节编辑工具
class ChapterEditorTool extends AgentTool {
  final Function(int, String) onChapterUpdated;

  ChapterEditorTool({
    required this.onChapterUpdated,
  }) : super(
          toolType: ToolType.chapterEditor,
          name: '章节编辑工具',
          description: '编辑和修改现有章节内容',
        );

  @override
  List<String> getRequiredParameters() {
    return [
      'chapter_number',
      'modifications',
      'original_content',
    ];
  }

  @override
  bool validateParameters(Map<String, dynamic> parameters) {
    final required = getRequiredParameters();
    return required.every((param) => parameters.containsKey(param));
  }

  @override
  Future<ToolResult> execute(Map<String, dynamic> parameters) async {
    try {
      if (!validateParameters(parameters)) {
        return ToolResult.failure('缺少必需参数');
      }

      final chapterNumber = parameters['chapter_number'] as int;
      final modifications = parameters['modifications'] as List;
      final originalContent = parameters['original_content'] as String;

      // 应用修改
      String modifiedContent = originalContent;
      final appliedModifications = <Map<String, dynamic>>[];

      for (final mod in modifications) {
        final modMap = mod as Map<String, dynamic>;
        final type = modMap['type'] as String;
        final startLine = modMap['start_line'] as int;
        final endLine = modMap['end_line'] as int;
        final newText = modMap['new_text'] as String;

        // 这里实现具体的文本修改逻辑
        // 简化实现，实际应该按行处理
        if (type == 'replace') {
          // 替换逻辑
          appliedModifications.add({
            'type': type,
            'start_line': startLine,
            'end_line': endLine,
            'applied': true,
          });
        }
      }

      // 通知章节更新
      onChapterUpdated(chapterNumber, modifiedContent);

      return ToolResult.success({
        'chapter_number': chapterNumber,
        'modified_content': modifiedContent,
        'applied_modifications': appliedModifications,
        'word_count': modifiedContent.length,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      return ToolResult.failure('章节编辑失败: $e');
    }
  }
}

/// 内容检索工具
class ContentRetrieverTool extends AgentTool {
  final Function(List<int>) getChaptersContent;

  ContentRetrieverTool({
    required this.getChaptersContent,
  }) : super(
          toolType: ToolType.contentRetriever,
          name: '内容检索工具',
          description: '检索和分析相关章节内容',
        );

  @override
  List<String> getRequiredParameters() {
    return ['target_chapters', 'query_type'];
  }

  @override
  bool validateParameters(Map<String, dynamic> parameters) {
    final required = getRequiredParameters();
    return required.every((param) => parameters.containsKey(param));
  }

  @override
  Future<ToolResult> execute(Map<String, dynamic> parameters) async {
    try {
      if (!validateParameters(parameters)) {
        return ToolResult.failure('缺少必需参数');
      }

      final targetChapters = List<int>.from(parameters['target_chapters']);
      final queryType = parameters['query_type'] as String;

      // 获取章节内容
      final chaptersContent = getChaptersContent(targetChapters);

      return ToolResult.success({
        'target_chapters': targetChapters,
        'query_type': queryType,
        'retrieved_content': chaptersContent,
        'retrieved_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      return ToolResult.failure('内容检索失败: $e');
    }
  }
}

/// 连贯性验证工具
class CoherenceValidatorTool extends AgentTool {
  CoherenceValidatorTool() : super(
          toolType: ToolType.coherenceValidator,
          name: '连贯性验证工具',
          description: '验证章节内容的连贯性和一致性',
        );

  @override
  List<String> getRequiredParameters() {
    return [
      'current_content',
      'previous_content',
      'character_settings',
      'world_settings',
    ];
  }

  @override
  bool validateParameters(Map<String, dynamic> parameters) {
    final required = getRequiredParameters();
    return required.every((param) => parameters.containsKey(param));
  }

  @override
  Future<ToolResult> execute(Map<String, dynamic> parameters) async {
    try {
      if (!validateParameters(parameters)) {
        return ToolResult.failure('缺少必需参数');
      }

      final currentContent = parameters['current_content'] as String;
      final previousContent = parameters['previous_content'] as String;
      final characterSettings = parameters['character_settings'] as String;
      final worldSettings = parameters['world_settings'] as String;

      // 执行连贯性检查
      final coherenceResult = await _performCoherenceCheck(
        currentContent,
        previousContent,
        characterSettings,
        worldSettings,
      );

      return ToolResult.success({
        'coherence_score': coherenceResult['score'],
        'timeline_consistent': coherenceResult['timeline_consistent'],
        'character_consistent': coherenceResult['character_consistent'],
        'world_building_consistent': coherenceResult['world_building_consistent'],
        'issues': coherenceResult['issues'],
        'suggestions': coherenceResult['suggestions'],
        'validated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      return ToolResult.failure('连贯性验证失败: $e');
    }
  }

  /// 执行连贯性检查
  Future<Map<String, dynamic>> _performCoherenceCheck(
    String currentContent,
    String previousContent,
    String characterSettings,
    String worldSettings,
  ) async {
    // 简化的连贯性检查逻辑
    // 实际实现中应该使用更复杂的NLP分析
    
    final issues = <String>[];
    final suggestions = <String>[];
    
    // 检查时间线一致性
    bool timelineConsistent = true;
    if (currentContent.contains('昨天') && previousContent.contains('明天')) {
      timelineConsistent = false;
      issues.add('时间线不一致');
      suggestions.add('检查时间表述的逻辑性');
    }
    
    // 检查角色一致性
    bool characterConsistent = true;
    // 这里可以添加更复杂的角色一致性检查
    
    // 检查世界观一致性
    bool worldBuildingConsistent = true;
    // 这里可以添加世界观一致性检查
    
    // 计算总体连贯性分数
    double score = 0.9; // 简化计算
    if (!timelineConsistent) score -= 0.3;
    if (!characterConsistent) score -= 0.3;
    if (!worldBuildingConsistent) score -= 0.3;
    
    return {
      'score': score,
      'timeline_consistent': timelineConsistent,
      'character_consistent': characterConsistent,
      'world_building_consistent': worldBuildingConsistent,
      'issues': issues,
      'suggestions': suggestions,
    };
  }
}

/// Apply工具 - 专门用于内容应用
class ApplyTool extends AgentTool {
  final ApplyService _applyService;

  ApplyTool({
    required ApplyService applyService,
  }) : _applyService = applyService,
       super(
         toolType: ToolType.chapterEditor, // 复用编辑工具类型
         name: '内容应用工具',
         description: '快速精确地将AI生成的内容应用到章节中',
       );

  @override
  List<String> getRequiredParameters() {
    return [
      'chapter_number',
      'application_type',
      'original_content',
      'new_content',
    ];
  }

  @override
  bool validateParameters(Map<String, dynamic> parameters) {
    final required = getRequiredParameters();
    return required.every((param) => parameters.containsKey(param));
  }

  @override
  Future<ToolResult> execute(Map<String, dynamic> parameters) async {
    try {
      if (!validateParameters(parameters)) {
        return ToolResult.failure('缺少必需参数');
      }

      final chapterNumber = parameters['chapter_number'] as int;
      final applicationType = parameters['application_type'] as String;
      final originalContent = parameters['original_content'] as String;
      final newContent = parameters['new_content'] as String;

      // 使用Apply服务应用内容
      final applyResult = await _applyService.applyContent(
        chapterNumber: chapterNumber,
        applicationType: applicationType,
        originalContent: originalContent,
        newContent: newContent,
      );

      return ToolResult.success({
        'apply_result': applyResult,
        'success': applyResult.success,
        'applied_content': applyResult.appliedContent,
        'word_count': applyResult.wordCount,
        'quality_score': applyResult.qualityCheck.coherenceScore,
      });
    } catch (e) {
      return ToolResult.failure('内容应用失败: $e');
    }
  }
}

/// 工具管理器
class AgentToolManager {
  final Map<ToolType, AgentTool> _tools = {};

  /// 注册工具
  void registerTool(AgentTool tool) {
    _tools[tool.toolType] = tool;
  }

  /// 获取工具
  AgentTool? getTool(ToolType toolType) {
    return _tools[toolType];
  }

  /// 执行工具
  Future<ToolResult> executeTool(
    ToolType toolType,
    Map<String, dynamic> parameters,
  ) async {
    final tool = getTool(toolType);
    if (tool == null) {
      return ToolResult.failure('工具不存在: $toolType');
    }

    return await tool.execute(parameters);
  }

  /// 获取所有已注册的工具
  List<AgentTool> getAllTools() {
    return _tools.values.toList();
  }

  /// 检查工具是否可用
  bool isToolAvailable(ToolType toolType) {
    return _tools.containsKey(toolType);
  }
}
