/// Agent功能演示页面
library agent_demo;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/agent_models.dart';
import '../providers/agent_provider.dart';
import '../widgets/agent_mode_switch.dart';
import '../widgets/agent_execution_panel.dart';
import '../../../controllers/api_config_controller.dart';

/// Agent演示页面
class AgentDemoPage extends StatelessWidget {
  const AgentDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 确保AgentProvider已初始化
    Get.lazyPut(() => AgentProvider(
      apiConfigController: Get.find<ApiConfigController>(),
    ));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Agent功能演示'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 功能介绍
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '岱宗AI Agent功能',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '类似Cursor IDE的Agent功能，专门用于长篇小说创作：\n'
                      '• 自主理解用户需求\n'
                      '• 制定执行计划\n'
                      '• 创建和编辑章节\n'
                      '• 确保内容连贯性',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Agent模式切换
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '模式切换',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const AgentModeSwitch(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Agent执行面板
            const Expanded(
              child: AgentExecutionPanel(),
            ),
            const SizedBox(height: 16),

            // 测试按钮
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '测试功能',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: () => _testAgentTask(context, '创建第1章：开始的冒险'),
                          child: const Text('测试章节创建'),
                        ),
                        ElevatedButton(
                          onPressed: () => _testAgentTask(context, '编辑第1章，增加更多细节'),
                          child: const Text('测试章节编辑'),
                        ),
                        ElevatedButton(
                          onPressed: () => _testAgentTask(context, '批量创建第2-5章'),
                          child: const Text('测试批量创建'),
                        ),
                        ElevatedButton(
                          onPressed: () => _testAgentTask(context, '分析当前情节发展'),
                          child: const Text('测试情节分析'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _testAgentTask(BuildContext context, String instruction) {
    try {
      final agentProvider = Get.find<AgentProvider>();
      
      if (agentProvider.currentMode != AgentMode.agent) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('请先切换到Agent模式'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // 模拟执行Agent任务
      _simulateAgentExecution(agentProvider, instruction);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('开始执行任务: $instruction'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('执行失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _simulateAgentExecution(AgentProvider agentProvider, String instruction) {
    // 这里模拟Agent执行过程
    // 在实际实现中，这会调用真正的Agent服务
    
    // 模拟开始执行
    agentProvider.switchMode(AgentMode.agent);
    
    // 模拟执行步骤
    Future.delayed(const Duration(seconds: 1), () {
      // 模拟执行完成
    });
  }
}

/// Agent演示控制器
class AgentDemoController extends GetxController {
  final RxString currentTask = ''.obs;
  final RxBool isExecuting = false.obs;
  final RxDouble progress = 0.0.obs;

  void startTask(String task) {
    currentTask.value = task;
    isExecuting.value = true;
    progress.value = 0.0;
    
    // 模拟进度更新
    _simulateProgress();
  }

  void _simulateProgress() {
    const duration = Duration(milliseconds: 100);
    const increment = 0.1;
    
    Timer.periodic(duration, (timer) {
      if (progress.value >= 1.0) {
        timer.cancel();
        isExecuting.value = false;
        currentTask.value = '';
        return;
      }
      
      progress.value += increment;
      if (progress.value > 1.0) {
        progress.value = 1.0;
      }
    });
  }

  void stopTask() {
    isExecuting.value = false;
    currentTask.value = '';
    progress.value = 0.0;
  }
}


