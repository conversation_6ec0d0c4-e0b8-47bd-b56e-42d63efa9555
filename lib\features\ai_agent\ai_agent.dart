/// AI Agent功能模块导出文件
library ai_agent;

// 模型
export 'models/agent_models.dart';

// 服务
export 'services/agent_service.dart';
export 'services/apply_service.dart';

// 工具
export 'tools/agent_tools.dart';

// 提供者
export 'providers/agent_provider.dart';

// 组件
export 'widgets/agent_mode_switch.dart';
export 'widgets/agent_confirmation_dialog.dart';

/// Agent功能初始化器
class AgentInitializer {
  static bool _initialized = false;

  /// 初始化Agent功能
  static void initialize() {
    if (_initialized) return;

    // 这里可以添加全局初始化逻辑
    // 例如注册全局服务、配置等

    _initialized = true;
  }

  /// 检查是否已初始化
  static bool get isInitialized => _initialized;
}
