/// Agent状态管理
library agent_provider;

import 'dart:async';
import 'package:get/get.dart';
import 'package:langchain/langchain.dart';
import '../models/agent_models.dart';
import '../services/agent_service.dart';
import '../services/apply_service.dart';
import '../tools/agent_tools.dart';
import '../../novel_generation/models/novel_models.dart';
import '../../../controllers/api_config_controller.dart';

/// Agent状态提供者
class AgentProvider extends GetxController {
  final ApiConfigController _apiConfigController;
  AgentService? _agentService;
  StreamSubscription<AgentEvent>? _eventSubscription;

  // 状态变量
  AgentMode _currentMode = AgentMode.chat;
  bool _isExecuting = false;
  bool _isPaused = false;
  ExecutionPlan? _currentPlan;
  ExecutionStep? _currentStep;
  List<AgentEvent> _eventHistory = [];
  String? _lastError;
  UserConfirmation? _pendingConfirmation;

  // 进度相关
  double _progress = 0.0;
  String _currentStepDescription = '';

  AgentProvider({
    required ApiConfigController apiConfigController,
  }) : _apiConfigController = apiConfigController {
    _initializeAgent();
  }

  // Getters
  AgentMode get currentMode => _currentMode;
  bool get isExecuting => _isExecuting;
  bool get isPaused => _isPaused;
  ExecutionPlan? get currentPlan => _currentPlan;
  ExecutionStep? get currentStep => _currentStep;
  List<AgentEvent> get eventHistory => List.unmodifiable(_eventHistory);
  String? get lastError => _lastError;
  UserConfirmation? get pendingConfirmation => _pendingConfirmation;
  double get progress => _progress;
  String get currentStepDescription => _currentStepDescription;

  /// 初始化Agent
  void _initializeAgent() {
    final currentModel = _apiConfigController.getCurrentModel();
    // 暂时使用简单的模拟对象，避免类型错误
    final llm = {
      'apiKey': currentModel.apiKey,
      'baseUrl': currentModel.apiUrl,
      'model': currentModel.model,
      'temperature': 0.7,
    };

    final toolManager = AgentToolManager();

    // 创建Apply服务
    final applyService = ApplyService(llm: llm);

    // 注册Apply工具
    final applyTool = ApplyTool(applyService: applyService);
    toolManager.registerTool(applyTool);

    _agentService = AgentService(
      llm: llm,
      toolManager: toolManager,
    );

    _subscribeToEvents();
  }

  /// 订阅Agent事件
  void _subscribeToEvents() {
    _eventSubscription?.cancel();
    _eventSubscription = _agentService?.events.listen(_handleAgentEvent);
  }

  /// 处理Agent事件
  void _handleAgentEvent(AgentEvent event) {
    _eventHistory.add(event);
    
    switch (event) {
      case AgentStarted started:
        _isExecuting = true;
        _isPaused = false;
        _lastError = null;
        _progress = 0.0;
        _currentStepDescription = '开始执行: ${started.taskDescription}';
        break;

      case AgentStepStarted stepStarted:
        _currentStep = stepStarted.step;
        _currentStepDescription = stepStarted.step.description;
        _updateProgress();
        break;

      case AgentStepCompleted stepCompleted:
        _currentStep = stepCompleted.step;
        _updateProgress();
        break;

      case AgentStepFailed stepFailed:
        _currentStep = stepFailed.step;
        _lastError = stepFailed.error;
        break;

      case AgentCompleted completed:
        _isExecuting = false;
        _isPaused = false;
        _currentPlan = completed.plan;
        _progress = 1.0;
        _currentStepDescription = '任务完成';
        break;

      case AgentFailed failed:
        _isExecuting = false;
        _isPaused = false;
        _lastError = failed.error;
        _currentStepDescription = '任务失败';
        break;

      case AgentPaused paused:
        _isPaused = true;
        _currentStepDescription = '已暂停: ${paused.reason}';
        break;

      case UserConfirmationRequired confirmationRequired:
        _pendingConfirmation = confirmationRequired.confirmation;
        _isPaused = true;
        _currentStepDescription = '等待用户确认: ${confirmationRequired.confirmation.reason}';
        break;
    }

    update();
  }

  /// 更新进度
  void _updateProgress() {
    if (_currentPlan != null && _currentStep != null) {
      final totalSteps = _currentPlan!.totalSteps;
      final currentStepIndex = _currentPlan!.currentStepIndex;
      _progress = totalSteps > 0 ? currentStepIndex / totalSteps : 0.0;
    }
  }

  /// 切换Agent模式
  void switchMode(AgentMode mode) {
    if (_isExecuting) {
      throw Exception('无法在执行过程中切换模式');
    }
    
    _currentMode = mode;
    update();
  }

  /// 执行Agent任务
  Future<void> executeAgentTask({
    required String userInstruction,
    required NovelData novelData,
    required List<ChapterData> chapters,
  }) async {
    if (_currentMode != AgentMode.agent) {
      throw Exception('当前不在Agent模式');
    }

    if (_agentService == null) {
      throw Exception('Agent服务未初始化');
    }

    // 准备小说信息
    final novelInfo = {
      'title': novelData.title,
      'genres': novelData.genres,
      'theme': novelData.theme,
      'targetReaders': novelData.targetReaders,
      'background': novelData.background,
      'characters': novelData.characters,
      'writingStyle': novelData.writingStylePrompt,
      'knowledgeBase': novelData.knowledgeBase,
      'otherRequirements': novelData.otherRequirements,
    };

    // 准备章节概览
    final chapterOverview = chapters.map((chapter) => {
      'chapterNumber': chapter.chapterNumber,
      'title': chapter.title,
      'wordCount': chapter.wordCount,
      'summary': chapter.content.length > 200
          ? '${chapter.content.substring(0, 200)}...'
          : chapter.content,
    }).toList().cast<Map<String, dynamic>>();

    await _agentService!.executeTask(
      userInstruction: userInstruction,
      novelTitle: novelData.title,
      genres: novelData.genres.join(', '),
      currentChapterCount: chapters.length,
      novelInfo: novelInfo,
      chapterOverview: chapterOverview,
    );
  }

  /// 暂停执行
  void pauseExecution(String reason) {
    _agentService?.pauseExecution(reason);
  }

  /// 恢复执行
  void resumeExecution() {
    _agentService?.resumeExecution();
  }

  /// 取消执行
  void cancelExecution() {
    _agentService?.cancelExecution();
  }

  /// 回滚到检查点
  Future<void> rollbackToCheckpoint(int stepId) async {
    await _agentService?.rollbackToCheckpoint(stepId);
  }

  /// 处理用户确认
  void handleUserConfirmation(bool confirmed) {
    if (_pendingConfirmation != null) {
      _agentService?.handleUserConfirmation(
        _pendingConfirmation!.stepId,
        confirmed,
      );
      _pendingConfirmation = null;
      update();
    }
  }

  /// 注册章节创建工具
  void registerChapterCreatorTool(Function(ChapterData) onChapterCreated) {
    final tool = ChapterCreatorTool(onChapterCreated: onChapterCreated);
    _agentService?.toolManager.registerTool(tool);
  }

  /// 注册章节编辑工具
  void registerChapterEditorTool(Function(int, String) onChapterUpdated) {
    final tool = ChapterEditorTool(onChapterUpdated: onChapterUpdated);
    _agentService?.toolManager.registerTool(tool);
  }

  /// 注册内容检索工具
  void registerContentRetrieverTool(Function(List<int>) getChaptersContent) {
    final tool = ContentRetrieverTool(getChaptersContent: getChaptersContent);
    _agentService?.toolManager.registerTool(tool);
  }

  /// 注册连贯性验证工具
  void registerCoherenceValidatorTool() {
    final tool = CoherenceValidatorTool();
    _agentService?.toolManager.registerTool(tool);
  }

  /// 清除错误
  void clearError() {
    _lastError = null;
    update();
  }

  /// 清除事件历史
  void clearEventHistory() {
    _eventHistory.clear();
    update();
  }

  /// 获取当前步骤进度文本
  String getProgressText() {
    if (!_isExecuting) {
      return '未执行';
    }

    if (_currentPlan == null) {
      return '准备中...';
    }

    final current = _currentPlan!.currentStepIndex + 1;
    final total = _currentPlan!.totalSteps;
    return '步骤 $current / $total';
  }

  /// 获取执行状态文本
  String getStatusText() {
    if (_lastError != null) {
      return '错误: $_lastError';
    }

    if (_isPaused) {
      return '已暂停';
    }

    if (_isExecuting) {
      return '执行中';
    }

    return '就绪';
  }

  /// 检查是否可以执行新任务
  bool canExecuteNewTask() {
    return !_isExecuting && _currentMode == AgentMode.agent;
  }

  /// 检查是否可以暂停
  bool canPause() {
    return _isExecuting && !_isPaused;
  }

  /// 检查是否可以恢复
  bool canResume() {
    return _isExecuting && _isPaused;
  }

  /// 检查是否可以取消
  bool canCancel() {
    return _isExecuting;
  }

  /// 检查是否有待确认的操作
  bool hasPendingConfirmation() {
    return _pendingConfirmation != null;
  }

  @override
  void onClose() {
    _eventSubscription?.cancel();
    _agentService?.dispose();
    super.onClose();
  }
}
