/// Agent Apply服务 - 专门用于内容应用
library apply_service;

import 'dart:convert';
import 'package:langchain/langchain.dart';
import '../../../langchain/prompts/novel_prompt_templates_enhanced.dart';

/// 内容应用结果
class ApplyResult {
  final bool success;
  final int chapterNumber;
  final String appliedContent;
  final List<ContentModification> modifications;
  final int wordCount;
  final QualityCheck qualityCheck;
  final bool backupCreated;
  final DateTime appliedAt;

  const ApplyResult({
    required this.success,
    required this.chapterNumber,
    required this.appliedContent,
    required this.modifications,
    required this.wordCount,
    required this.qualityCheck,
    required this.backupCreated,
    required this.appliedAt,
  });

  factory ApplyResult.fromJson(Map<String, dynamic> json) {
    final applyResult = json['apply_result'] as Map<String, dynamic>;
    
    return ApplyResult(
      success: applyResult['success'] ?? false,
      chapterNumber: applyResult['chapter_number'] ?? 0,
      appliedContent: applyResult['applied_content'] ?? '',
      modifications: (applyResult['modifications'] as List?)
          ?.map((m) => ContentModification.fromJson(m))
          .toList() ?? [],
      wordCount: applyResult['word_count'] ?? 0,
      qualityCheck: QualityCheck.fromJson(applyResult['quality_check'] ?? {}),
      backupCreated: applyResult['backup_created'] ?? false,
      appliedAt: DateTime.tryParse(applyResult['applied_at'] ?? '') ?? DateTime.now(),
    );
  }
}

/// 内容修改记录
class ContentModification {
  final String type; // replace, insert, delete
  final int startPosition;
  final int endPosition;
  final String originalText;
  final String newText;
  final bool applied;

  const ContentModification({
    required this.type,
    required this.startPosition,
    required this.endPosition,
    required this.originalText,
    required this.newText,
    required this.applied,
  });

  factory ContentModification.fromJson(Map<String, dynamic> json) {
    return ContentModification(
      type: json['type'] ?? '',
      startPosition: json['start_position'] ?? 0,
      endPosition: json['end_position'] ?? 0,
      originalText: json['original_text'] ?? '',
      newText: json['new_text'] ?? '',
      applied: json['applied'] ?? false,
    );
  }
}

/// 质量检查结果
class QualityCheck {
  final double coherenceScore;
  final bool consistencyCheck;
  final List<String> issues;
  final List<String> suggestions;

  const QualityCheck({
    required this.coherenceScore,
    required this.consistencyCheck,
    required this.issues,
    required this.suggestions,
  });

  factory QualityCheck.fromJson(Map<String, dynamic> json) {
    return QualityCheck(
      coherenceScore: (json['coherence_score'] ?? 0.0).toDouble(),
      consistencyCheck: json['consistency_check'] ?? false,
      issues: List<String>.from(json['issues'] ?? []),
      suggestions: List<String>.from(json['suggestions'] ?? []),
    );
  }
}

/// 连贯性验证结果
class CoherenceResult {
  final double overallScore;
  final int chapterNumber;
  final Map<String, CoherenceCheck> checks;
  final List<CriticalIssue> criticalIssues;
  final List<String> recommendations;
  final DateTime validatedAt;

  const CoherenceResult({
    required this.overallScore,
    required this.chapterNumber,
    required this.checks,
    required this.criticalIssues,
    required this.recommendations,
    required this.validatedAt,
  });

  factory CoherenceResult.fromJson(Map<String, dynamic> json) {
    final coherenceResult = json['coherence_result'] as Map<String, dynamic>;
    
    return CoherenceResult(
      overallScore: (coherenceResult['overall_score'] ?? 0.0).toDouble(),
      chapterNumber: coherenceResult['chapter_number'] ?? 0,
      checks: (coherenceResult['checks'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(key, CoherenceCheck.fromJson(value)))
          ?? {},
      criticalIssues: (coherenceResult['critical_issues'] as List?)
          ?.map((issue) => CriticalIssue.fromJson(issue))
          .toList() ?? [],
      recommendations: List<String>.from(coherenceResult['recommendations'] ?? []),
      validatedAt: DateTime.tryParse(coherenceResult['validated_at'] ?? '') ?? DateTime.now(),
    );
  }
}

/// 连贯性检查项
class CoherenceCheck {
  final double score;
  final bool passed;
  final List<String> issues;
  final List<String> suggestions;

  const CoherenceCheck({
    required this.score,
    required this.passed,
    required this.issues,
    required this.suggestions,
  });

  factory CoherenceCheck.fromJson(Map<String, dynamic> json) {
    return CoherenceCheck(
      score: (json['score'] ?? 0.0).toDouble(),
      passed: json['passed'] ?? false,
      issues: List<String>.from(json['issues'] ?? []),
      suggestions: List<String>.from(json['suggestions'] ?? []),
    );
  }
}

/// 关键问题
class CriticalIssue {
  final String type;
  final String severity;
  final String description;
  final String location;
  final String suggestion;

  const CriticalIssue({
    required this.type,
    required this.severity,
    required this.description,
    required this.location,
    required this.suggestion,
  });

  factory CriticalIssue.fromJson(Map<String, dynamic> json) {
    return CriticalIssue(
      type: json['type'] ?? '',
      severity: json['severity'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      suggestion: json['suggestion'] ?? '',
    );
  }
}

/// Apply服务 - 类似Cursor的Apply模型
class ApplyService {
  final dynamic _llm; // 暂时使用dynamic避免类型错误

  ApplyService({required dynamic llm}) : _llm = llm;

  /// 应用内容到章节
  Future<ApplyResult> applyContent({
    required int chapterNumber,
    required String applicationType,
    required String originalContent,
    required String newContent,
  }) async {
    final prompt = NovelPromptTemplates.agentApplyTemplate.format({
      'chapterNumber': chapterNumber.toString(),
      'applicationType': applicationType,
      'originalContent': originalContent,
      'newContent': newContent,
    });

    try {
      final response = await _llm.invoke(PromptValue.string(prompt));
      final responseText = response.output.content;

      final jsonData = jsonDecode(responseText);
      return ApplyResult.fromJson(jsonData);
    } catch (e) {
      // 如果AI响应解析失败，创建一个简单的应用结果
      return ApplyResult(
        success: false,
        chapterNumber: chapterNumber,
        appliedContent: originalContent,
        modifications: [],
        wordCount: originalContent.length,
        qualityCheck: const QualityCheck(
          coherenceScore: 0.0,
          consistencyCheck: false,
          issues: ['应用失败'],
          suggestions: ['请重试'],
        ),
        backupCreated: false,
        appliedAt: DateTime.now(),
      );
    }
  }

  /// 验证内容连贯性
  Future<CoherenceResult> validateCoherence({
    required int chapterNumber,
    required String currentContent,
    required String previousContent,
    required String characterSettings,
    required String worldSettings,
  }) async {
    final prompt = NovelPromptTemplates.agentCoherenceTemplate.format({
      'chapterNumber': chapterNumber.toString(),
      'currentContent': currentContent,
      'previousContent': previousContent,
      'characterSettings': characterSettings,
      'worldSettings': worldSettings,
    });

    try {
      final response = await _llm.invoke(PromptValue.string(prompt));
      final responseText = response.output.content;

      final jsonData = jsonDecode(responseText);
      return CoherenceResult.fromJson(jsonData);
    } catch (e) {
      // 如果AI响应解析失败，创建一个简单的验证结果
      return CoherenceResult(
        overallScore: 0.5,
        chapterNumber: chapterNumber,
        checks: {},
        criticalIssues: [],
        recommendations: ['验证失败，请重试'],
        validatedAt: DateTime.now(),
      );
    }
  }

  /// 快速应用简单修改
  String quickApply(String originalContent, List<ContentModification> modifications) {
    String result = originalContent;
    
    // 按位置倒序排列，避免位置偏移
    final sortedModifications = modifications.toList()
      ..sort((a, b) => b.startPosition.compareTo(a.startPosition));

    for (final modification in sortedModifications) {
      try {
        switch (modification.type) {
          case 'replace':
            if (modification.startPosition >= 0 && 
                modification.endPosition <= result.length &&
                modification.startPosition <= modification.endPosition) {
              result = result.substring(0, modification.startPosition) +
                      modification.newText +
                      result.substring(modification.endPosition);
            }
            break;
          case 'insert':
            if (modification.startPosition >= 0 && 
                modification.startPosition <= result.length) {
              result = result.substring(0, modification.startPosition) +
                      modification.newText +
                      result.substring(modification.startPosition);
            }
            break;
          case 'delete':
            if (modification.startPosition >= 0 && 
                modification.endPosition <= result.length &&
                modification.startPosition <= modification.endPosition) {
              result = result.substring(0, modification.startPosition) +
                      result.substring(modification.endPosition);
            }
            break;
        }
      } catch (e) {
        // 忽略单个修改的错误，继续处理其他修改
        continue;
      }
    }

    return result;
  }
}
