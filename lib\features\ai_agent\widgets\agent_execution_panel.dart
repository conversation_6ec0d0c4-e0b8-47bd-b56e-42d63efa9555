/// Agent执行面板组件
library agent_execution_panel;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/agent_provider.dart';
import '../models/agent_models.dart';

/// Agent执行面板
class AgentExecutionPanel extends StatelessWidget {
  const AgentExecutionPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AgentProvider>(
      builder: (agentProvider) {
        if (agentProvider.currentMode != AgentMode.agent) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                children: [
                  Icon(
                    Icons.auto_awesome,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Agent执行状态',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  if (agentProvider.isExecuting)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '执行中',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),

              // 进度信息
              if (agentProvider.isExecuting) ...[
                _buildProgressSection(context, agentProvider),
                const SizedBox(height: 16),
              ],

              // 当前步骤
              if (agentProvider.currentStep != null) ...[
                _buildCurrentStepSection(context, agentProvider),
                const SizedBox(height: 16),
              ],

              // 错误信息
              if (agentProvider.lastError != null) ...[
                _buildErrorSection(context, agentProvider),
                const SizedBox(height: 16),
              ],

              // 控制按钮
              _buildControlButtons(context, agentProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressSection(BuildContext context, AgentProvider agentProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: agentProvider.progress,
                backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              agentProvider.getProgressText(),
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          agentProvider.currentStepDescription,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildCurrentStepSection(BuildContext context, AgentProvider agentProvider) {
    final step = agentProvider.currentStep!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getStepIcon(step.stepType),
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                '步骤 ${step.stepId}: ${_getStepTypeName(step.stepType)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              _buildStepStatusChip(context, step.status),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            step.description,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSection(BuildContext context, AgentProvider agentProvider) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              agentProvider.lastError!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
          IconButton(
            onPressed: agentProvider.clearError,
            icon: const Icon(Icons.close, size: 16),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons(BuildContext context, AgentProvider agentProvider) {
    return Row(
      children: [
        if (agentProvider.canPause())
          _buildControlButton(
            context,
            icon: Icons.pause,
            label: '暂停',
            onPressed: () => agentProvider.pauseExecution('用户暂停'),
          ),
        if (agentProvider.canResume()) ...[
          const SizedBox(width: 8),
          _buildControlButton(
            context,
            icon: Icons.play_arrow,
            label: '继续',
            onPressed: agentProvider.resumeExecution,
          ),
        ],
        if (agentProvider.canCancel()) ...[
          const SizedBox(width: 8),
          _buildControlButton(
            context,
            icon: Icons.stop,
            label: '取消',
            onPressed: agentProvider.cancelExecution,
            isDestructive: true,
          ),
        ],
        const Spacer(),
        if (agentProvider.eventHistory.isNotEmpty)
          TextButton.icon(
            onPressed: () => _showEventHistory(context, agentProvider),
            icon: const Icon(Icons.history, size: 16),
            label: const Text('查看日志'),
          ),
      ],
    );
  }

  Widget _buildControlButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isDestructive = false,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        size: 16,
        color: isDestructive ? Theme.of(context).colorScheme.error : null,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isDestructive ? Theme.of(context).colorScheme.error : null,
        ),
      ),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        side: BorderSide(
          color: isDestructive 
              ? Theme.of(context).colorScheme.error
              : Theme.of(context).colorScheme.outline,
        ),
      ),
    );
  }

  Widget _buildStepStatusChip(BuildContext context, ExecutionStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case ExecutionStatus.pending:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        text = '等待';
        break;
      case ExecutionStatus.running:
        backgroundColor = Theme.of(context).colorScheme.primary;
        textColor = Theme.of(context).colorScheme.onPrimary;
        text = '执行中';
        break;
      case ExecutionStatus.completed:
        backgroundColor = Colors.green;
        textColor = Colors.white;
        text = '完成';
        break;
      case ExecutionStatus.failed:
        backgroundColor = Theme.of(context).colorScheme.error;
        textColor = Theme.of(context).colorScheme.onError;
        text = '失败';
        break;
      case ExecutionStatus.cancelled:
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        text = '取消';
        break;
      case ExecutionStatus.paused:
        backgroundColor = Colors.amber;
        textColor = Colors.black;
        text = '暂停';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: textColor,
          fontSize: 10,
        ),
      ),
    );
  }

  IconData _getStepIcon(StepType stepType) {
    switch (stepType) {
      case StepType.analyze:
        return Icons.analytics;
      case StepType.create:
        return Icons.create;
      case StepType.edit:
        return Icons.edit;
      case StepType.validate:
        return Icons.check_circle;
    }
  }

  String _getStepTypeName(StepType stepType) {
    switch (stepType) {
      case StepType.analyze:
        return '分析';
      case StepType.create:
        return '创建';
      case StepType.edit:
        return '编辑';
      case StepType.validate:
        return '验证';
    }
  }

  void _showEventHistory(BuildContext context, AgentProvider agentProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Agent执行日志'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: agentProvider.eventHistory.length,
            itemBuilder: (context, index) {
              final event = agentProvider.eventHistory[index];
              return ListTile(
                dense: true,
                title: Text(event.runtimeType.toString()),
                subtitle: Text(_getEventDescription(event)),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  String _getEventDescription(dynamic event) {
    final eventType = event.runtimeType.toString();
    switch (eventType) {
      case 'AgentStarted':
        return '开始执行任务';
      case 'AgentCompleted':
        return '任务执行完成';
      case 'AgentFailed':
        return '任务执行失败';
      case 'AgentPaused':
        return '任务已暂停';
      default:
        return '未知事件: $eventType';
    }
  }
}
