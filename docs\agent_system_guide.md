# 岱宗AI辅助创作助手 - Agent系统使用指南

## 概述

岱宗AI辅助创作助手现已集成类似Cursor IDE的Agent功能，专门用于长篇小说创作。Agent系统能够自主理解用户需求、制定执行计划、创建和编辑章节内容，并确保内容的连贯性和一致性。

## 核心功能

### 1. 双模式操作
- **聊天模式**：传统的AI对话模式，提供建议和指导
- **Agent模式**：自主执行模式，能够直接创建和修改章节内容

### 2. 多层次模型协作
- **理解模型**：分析用户需求和任务复杂度
- **规划模型**：制定详细的执行计划和步骤分解
- **执行模型**：生成具体的代码修改和内容创建
- **Apply模型**：快速精确地应用内容修改
- **验证模型**：检查结果质量和连贯性

### 3. 专业工具系统
- **章节创建工具**：根据大纲自动创建新章节
- **章节编辑工具**：智能编辑现有章节内容
- **内容检索工具**：快速查找和分析相关章节
- **连贯性验证工具**：确保故事的一致性和逻辑性
- **Apply工具**：专门用于内容应用的高效工具

## 使用方法

### 启动Agent模式

1. 打开岱宗AI界面
2. 点击右上角的"Agent模式"切换按钮
3. 界面会显示Agent状态指示器和进度面板

### Agent任务指令

Agent系统能够识别以下类型的指令：

#### 章节创建任务
```
创建第5章：主角的第一次冒险
生成3个新章节，主题是角色成长
写一章关于反派登场的内容
```

#### 章节编辑任务
```
编辑第3章，增加更多的环境描写
修改第7章的对话，让角色更生动
更新第10章的结尾，增加悬念
```

#### 批量操作
```
批量创建第11-15章
一次性生成5个章节的大纲
批量修改前3章的文风
```

#### 分析任务
```
分析当前情节发展
检查角色设定的一致性
验证世界观的连贯性
```

### Agent执行流程

1. **任务理解**：Agent分析用户指令，确定任务类型和复杂度
2. **执行规划**：制定详细的执行计划，包括步骤分解和检查点
3. **用户确认**：对于重要操作，Agent会请求用户确认
4. **逐步执行**：按计划执行每个步骤，实时显示进度
5. **质量验证**：检查生成内容的质量和连贯性
6. **结果应用**：将最终结果应用到小说文件中

## 界面功能

### Agent模式切换器
- 位于界面右上角
- 支持在聊天模式和Agent模式之间切换
- 显示当前模式状态

### Agent状态指示器
- 显示Agent当前状态（就绪/执行中/暂停/错误）
- 实时更新执行进度

### Agent进度面板
- 显示当前执行的步骤和进度
- 提供暂停、继续、取消等控制按钮
- 显示详细的执行日志

### 用户确认对话框
- 对于重要操作，Agent会弹出确认对话框
- 用户可以选择确认或拒绝Agent的操作
- 支持查看详细的操作计划

## 技术特性

### Apply机制
- 类似Cursor IDE的Apply模型
- 专门用于快速精确地应用内容修改
- 支持大规模文本处理（数千行内容）
- 秒级应用速度

### 连贯性检查
- 自动验证时间线一致性
- 检查角色性格和状态的连续性
- 确保世界观设定的统一性
- 验证情节发展的逻辑性

### 错误处理和恢复
- 自动检测和修复常见错误
- 支持回滚到之前的检查点
- 提供详细的错误信息和修复建议

### 上下文管理
- 智能管理小说的历史章节内容
- 动态加载相关上下文信息
- 优化内存使用和处理速度

## 最佳实践

### 1. 明确指令
- 使用具体、明确的指令
- 指定章节编号和具体要求
- 提供足够的上下文信息

### 2. 分步执行
- 对于复杂任务，建议分步执行
- 先创建大纲，再生成具体内容
- 逐章验证和调整

### 3. 定期检查
- 定期检查生成内容的质量
- 及时发现和修正问题
- 保持故事的整体一致性

### 4. 备份管理
- Agent会自动创建备份
- 重要修改前手动创建检查点
- 定期保存工作进度

## 注意事项

1. **模型配置**：确保已正确配置AI模型和API密钥
2. **网络连接**：Agent需要稳定的网络连接
3. **文件权限**：确保应用有足够的文件读写权限
4. **内容审查**：建议人工审查Agent生成的内容
5. **版本控制**：重要修改前建议创建版本备份

## 故障排除

### 常见问题

1. **Agent无响应**
   - 检查网络连接
   - 验证API配置
   - 重启应用

2. **内容质量问题**
   - 调整模型参数
   - 提供更详细的指令
   - 使用连贯性验证工具

3. **执行失败**
   - 查看错误日志
   - 检查文件权限
   - 尝试分步执行

### 获取帮助

如果遇到问题，可以：
1. 查看Agent执行日志
2. 使用聊天模式咨询AI
3. 参考应用内的帮助文档
4. 联系技术支持

## 更新日志

### v1.0.0
- 初始版本发布
- 基础Agent功能
- 章节创建和编辑工具
- Apply机制实现

### 未来计划
- 增强连贯性检查算法
- 支持更多文件格式
- 添加协作功能
- 优化性能和稳定性
